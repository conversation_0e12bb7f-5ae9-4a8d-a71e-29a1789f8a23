import React, { useEffect, useState } from 'react'
import { But<PERSON>, DatePicker, Form, Input, Popconfirm, Select, Table } from 'antd'
import COLOR from '../../common/color'
import {
  ACTION_VISIT_HISTORY,
  PROCESSING_STATUS,
  TABLE_XML_TABLES_LIST_TAB_COLUMNS,
} from './VisitConstant'
import DetailXMLToolPage from '../Tool/DetailXmlToolPage'
import { getSSTable1sByPatientVisitId } from './VisitService'
import PropTypes from '../../common/PropTypes'
import { useUI } from '../../common/UIProvider'
import * as XLSX from 'xlsx'
import { handleError } from '../../common/helpers'
import { getAllXmlData } from './VisitHelpers'
import { deleteListItemService, getItemService } from '../../common/services'
import lists from '../../common/lists'
import { usePatientVisitHistory } from './hooks/usePatientVisitHistory'
import AsyncButton from '../../common/components/AsyncButton'
import useSSXmlTable from './hooks/useSSXmlTable'

const propTypes = {
  selectedTabKey: PropTypes.string,
  selectedPatientVisitMappingViews: PropTypes.array,
  currentPatientVisit: PropTypes.object,
  refetchPatientVisit: PropTypes.func,
  onSave: PropTypes.func,
}

const XMLTablesListTab = ({
  selectedTabKey,
  selectedPatientVisitMappingViews,
  currentPatientVisit,
  refetchPatientVisit,
  onSave,
}) => {
  //hooks
  const ui = useUI()
  const { addPatientVisitHistory } = usePatientVisitHistory()
  const [form] = Form.useForm()
  // state
  const [xmlData, setXmlData] = useState([])
  const [detailView, setDetailView] = useState(false) // State to toggle views
  const [selectedSsTable1, setSelectedSsTable1] = useState(null) // Store selected data for Step 2
  const isSyncing =
    currentPatientVisit?.processing_status == PROCESSING_STATUS.WAITING_XML_CREATION.name_e
  //variables
  const { RangePicker } = DatePicker

  const {
    ssTable1,
    allTables: allSSXmlTables,
    refetchAllSsXmlTables,
  } = useSSXmlTable(currentPatientVisit?.patient_visit_id)
  // function to fetch data
  const getInitData = async (formValues) => {
    setDetailView(false) // Reset detail view when fetching new data
    try {
      // Get form values if provided, otherwise use current form values
      const values = formValues || form.getFieldsValue()

      ui.setLoading(true)

      // Build filter parameters based on form values
      let filterParams = {}

      if (values.ma_lk) {
        filterParams.ma_lk = values.ma_lk
      }

      if (values.date && values.date.length === 2) {
        filterParams.startDate = values.date[0].format('YYYY-MM-DD')
        filterParams.endDate = values.date[1].format('YYYY-MM-DD')
      }

      if (values.status) {
        filterParams.status = values.status
      }

      // Simulate a data fetch with a timeout
      // const response = await getSSTable1sByPatientVisitId(
      //   '4ea414d5-32f0-40e0-37b3-08dcc7fd4db6' // selectedVisitDataList[0]?.patient_visit_id
      // )
      // const response = await getSSTable1sByPatientVisitId(
      //   selectedVisitDataList[0]?.patient_visit_id
      // )

      // Call API with filter parameters
      const response = await getSSTable1sByPatientVisitId(
        currentPatientVisit?.patient_visit_id,
        filterParams,
      )

      setXmlData(response.value)
      ui.setLoading(false)
      // setXmlData(TABLE_XML_BHYT_DATASOURCE)
    } catch (error) {
      ui.setLoading(false)
      handleError(error, 'XMLTablesListTab>getInitData')
    }
  }

  useEffect(() => {
    let autoRefreshSyncStatus

    if (isSyncing) {
      autoRefreshSyncStatus = setInterval(async () => {
        try {
          const pvisit = await getItemService(
            lists.patient_visit_mapping_view,
            currentPatientVisit?.patient_visit_id,
          )

          if (!(pvisit?.processing_status == 'WAITING_XML_CREATION')) {
            refetchPatientVisit()
            refetchAllSsXmlTables()
            clearInterval(autoRefreshSyncStatus)
            onSave()
            getInitData()
          }
        } catch (error) {
          handleError(error, 'autoRefreshSyncStatus')
        }
      }, 10000) // change to 10s
    }

    return () => clearInterval(autoRefreshSyncStatus)
  }, [currentPatientVisit?.processing_status])

  const exportToExcel = async (record, getDetails = false) => {
    try {
      const allData = await getAllXmlData(record.table_1_id, null, null, getDetails)

      // Create workbook
      const wb = XLSX.utils.book_new()

      // Add each table data as a separate sheet, even if empty
      allData.forEach(({ sheetName, data }) => {
        // Create worksheet with data or empty array if no data
        const ws = XLSX.utils.json_to_sheet(data.length > 0 ? data : [{}])
        XLSX.utils.book_append_sheet(wb, ws, sheetName)
      })

      // Save the file
      XLSX.writeFile(wb, `XML_Data_${record.invoice_no_}.xlsx`)
      ui.notiSuccess('Xuất file Excel thành công')
    } catch (error) {
      handleError(error)
      ui.notiError('Không thể xuất file Excel')
    }
  }

  // useEffect to call getInitData on component mount
  useEffect(() => {
    if (selectedTabKey === '5' && selectedPatientVisitMappingViews.length > 0) {
      getInitData()
    }
  }, [selectedTabKey])
  const handleDetailClick = (record) => {
    // Handle detail button click
    setSelectedSsTable1(record) // Set the selected record
    setDetailView(true) // Toggle to Step 2 view
  }
  // Handle back button
  const handleBack = async () => {
    setDetailView(false) // Toggle back to Step 1
    setSelectedSsTable1(null) // Clear selected data
    form.resetFields() // Reset form fields
    await getInitData()
  }
  if (!xmlData) {
    return <div>Loading...</div>
  }

  return (
    <>
      {detailView ? (
        <DetailXMLToolPage
          currentPatientVisit={currentPatientVisit}
          selectedSsTable1={selectedSsTable1}
          onBack={handleBack}
          isCompactView={true}
        />
      ) : (
        <div className="container-fluid">
          {/* <div>mã visit:{selectedVisitDataList[0]?.patient_visit_id}</div> */}
          <div>mã visit: {currentPatientVisit?.patient_visit_id}</div>

          <Form
            form={form}
            layout="inline"
            className="d-flex justify-content-start"
            onFinish={getInitData}>
            <Form.Item label="MA_LK" name="ma_lk">
              <Input />
            </Form.Item>
            <Form.Item label="Ngày" name="date">
              <RangePicker />
            </Form.Item>
            <Form.Item label="Trạng thái" name="status">
              <Select
                style={{ width: 120 }}
                options={[
                  { value: 'active', label: 'Active' },
                  { value: 'inactive', label: 'Inactive' },
                ]}
              />
            </Form.Item>
            <Form.Item>
              <Button
                icon={<i className="fa-solid fa-search ms-1"></i>}
                type="primary"
                htmlType="submit">
                Tìm kiếm
              </Button>
            </Form.Item>
            <Form.Item>
              <Button
                icon={<i className="fa-solid fa-rotate ms-1"></i>}
                onClick={() => {
                  form.resetFields()
                  getInitData()
                }}>
                Làm mới
              </Button>
            </Form.Item>
            {isSyncing && (
              <Form.Item>
                <Button loading={isSyncing} icon={<i className="fa-solid fa-rotate ms-1"></i>}>
                  ĐANG TẠO BẢNG XML
                </Button>
              </Form.Item>
            )}
          </Form>
          <Table
            size="small"
            className="mt-2 mb-2 custom-table"
            columns={[
              ...TABLE_XML_TABLES_LIST_TAB_COLUMNS,
              {
                title: 'Hành động',
                key: 'action',
                render: (_, record) => (
                  <div className="d-flex gap-2">
                    <Button
                      onClick={() => handleDetailClick(record)}
                      variant="outlined"
                      color="blue">
                      Chi tiết
                    </Button>
                    <AsyncButton
                      onClick={() => exportToExcel(record)}
                      style={{ color: COLOR.lime }}>
                      Tải XML
                    </AsyncButton>
                    <AsyncButton
                      onClick={() => exportToExcel(record, true)}
                      style={{ color: COLOR.lime }}>
                      Tải XML chi tiết
                    </AsyncButton>
                    <Popconfirm
                      title="Bạn có chắc chắn muốn xóa?"
                      okText="Yes"
                      cancelText="No"
                      onConfirm={async () => {
                        await deleteListItemService(lists.ss_table_1, record?.table_1_id)
                        const historyData = {
                          patient_visit_id: currentPatientVisit?.patient_visit_id,
                          note: currentPatientVisit?.note,
                        }

                        addPatientVisitHistory.mutateAsync({
                          historyData,
                          action: ACTION_VISIT_HISTORY.DELETE_XML,
                        })
                        await getInitData()
                      }}>
                      {' '}
                      <Button danger>Hủy bảng xml</Button>
                    </Popconfirm>
                  </div>
                ),
              },
            ]}
            dataSource={xmlData.map((item, index) => ({ ...item, key: index }))}
            pagination={false}
          />
        </div>
      )}
    </>
  )
}

XMLTablesListTab.propTypes = propTypes
export default XMLTablesListTab
