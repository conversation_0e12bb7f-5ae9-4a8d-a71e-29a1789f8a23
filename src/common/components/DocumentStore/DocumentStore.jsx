// src/components/UploadCard.js
import React, { useEffect, useState } from 'react'
import { Button, Form, Image, Input, Modal } from 'antd'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faClose, faSave, faUpload } from '@fortawesome/free-solid-svg-icons'
import <PERSON>agger from 'antd/es/upload/Dragger'
import {
  addFolderService,
  deleteFileService,
  deleteFolderService,
  getFileService,
  getItemsService,
  updateListItemService,
  uploadFileToDocLibService,
} from '../../services'
import lists from '../../lists'
import PropTypes from '../../PropTypes'
import { getFileExtension, handleError, removeGuidFromFileName } from '../../helpers'
import fileDownload from 'js-file-download'
import heic2any from 'heic2any'
import { useUI } from '../../UIProvider'
import PreviewFile from '../PreviewFile'
import DocumentItem from './DocumentItem'
import DocumentURl from './DocumentURl'
import { v4 } from 'uuid'
import { supportDocumentTypeList } from '.'
import imgSpinner from '../../../assets/spinner_v2.gif'

const propTypes = {
  list: PropTypes.object,
  dataSource: PropTypes.string,
  mode: PropTypes.string,
  folder: PropTypes.string,
  storeID: PropTypes.string,
  setAttachments: PropTypes.func,
  parentID: PropTypes.number,
  accept: PropTypes.string,
  maxCount: PropTypes.number,
  maxFileSize: PropTypes.object,
}

function DocumentStore({
  list = lists.StoreRecords,
  dataSource = null,
  mode = 'Edit',
  folder = 'Store',
  storeID,
  setAttachments,
  parentID = null,
  maxFileSize = { size: 25, unit: 'MB' },
  maxCount = Infinity,
  accept = '',
}) {
  const [fileList, setFileList] = useState([])
  const [selectedDeleteFile, setSelectedDeleteFile] = useState(null)
  const [selectedFile, setSelectedFile] = useState(null)
  const [selectedFolder, setSelectedFolder] = useState(null)
  const [currentParentId, setCurrentParentId] = useState([
    { folderName: 'abcde_Root', URL: folder, Id: parentID },
  ])
  const [isShowSaveFolder, setIsShowSaveFolder] = useState(false)
  const [isGettingFiles, setIsGettingFiles] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [isSavingFolder, setIsSavingFolder] = useState(false)
  const [isDeletingItem, setIsDeletingItem] = useState(false)
  const [folderFormMode, setFolderFormMode] = useState('')

  const ui = useUI()
  const [form] = Form.useForm()

  const beforeUpload = (file) => {
    const acceptedFileTypes = accept ? accept.split(',').map((type) => type.trim()) : null
    const isAcceptedFileType = acceptedFileTypes ? acceptedFileTypes.includes(file.type) : true
    if (!isAcceptedFileType) {
      ui.notiError(`You can only upload ${accept} file types!`)
    }

    const maxSizeInBytes = maxFileSize.size * (maxFileSize.unit === 'MB' ? 1024 * 1024 : 1024)
    const isBelowMaxSize = file.size < maxSizeInBytes
    if (!isBelowMaxSize) {
      ui.notiError(`File must be smaller than ${maxFileSize.size}${maxFileSize.unit}!`)
    }

    return isAcceptedFileType && isBelowMaxSize
  }

  const handleGetFiles = async () => {
    setIsGettingFiles(true)
    let data
    if (parentID !== null && storeID !== null && dataSource !== null) {
      data = await getItemsService(lists.StoreRecords, {
        filter: `RefID eq ${storeID} and DataSource eq '${dataSource}' and ParentID eq ${parentID}`,
        orderBy: 'Created desc ',
      })
      setFileList(data.value)
    } else if (dataSource !== null && parentID !== null) {
      data = await getItemsService(lists.StoreRecords, {
        filter: `DataSource eq '${dataSource}' and ParentID eq ${parentID}`,
        orderBy: 'Created desc ',
      })
      setFileList(data.value)
    } else if (parentID !== null && storeID === null && dataSource === null) {
      data = await getItemsService(lists.StoreRecords, {
        filter: `IsFolder eq true and ParentID eq ${parentID}`,
      })
      setFileList(data.value)
    }
    setIsGettingFiles(false)
  }

  // listen fileList
  useEffect(() => {
    if (setAttachments) {
      setAttachments(fileList)
    }
  }, [fileList])

  const handleDeleteFile = async () => {
    setIsDeletingItem(true)
    if (!selectedDeleteFile.IsFolder) {
      await deleteFileService(selectedDeleteFile.ServerRelativeUrl)
    } else {
      await deleteFolderService(selectedDeleteFile.ServerRelativeUrl)
    }
    setFileList(fileList.filter((i) => i.Id !== selectedDeleteFile.Id))
    if (setAttachments) {
      setAttachments(fileList.filter((i) => i.Id !== selectedDeleteFile.Id))
    }

    setSelectedDeleteFile(null)
    setIsDeletingItem(false)
  }

  const handleUploadFile = async ({ file }) => {
    setIsUploading(true)
    const ext = file.name.split('.').pop().toLocaleLowerCase()

    try {
      let rawFile = file

      if (ext === 'heic') {
        rawFile = await heic2any({ blob: new Blob([rawFile], { type: rawFile.type }) })
      }

      const uniqueFileName = `${v4()}_${rawFile.name}`
      //  upload to sharepoint
      await uploadFileToDocLibService(
        '/DocumentStore',
        uniqueFileName,
        storeID,
        dataSource,
        rawFile,
      )
      handleGetFiles()
    } catch (error) {
      handleError(error, 'handleUpload')
    }
    setIsUploading(false)
  }

  const handleOnClickFile = async (item) => {
    //item is folder

    if (item.IsFolder) {
      setCurrentParentId((prev) => [
        ...prev,
        { folderName: item.Name, URL: item.ServerRelativeUrl, Id: item.Id },
      ])
      const data = await getItemsService(lists.StoreRecords, {
        filter: `ParentID eq ${item.Id}`,
      })
      setFileList(data.value)
    }
    //item is file
    else {
      ui.setLoading(true)

      let file = item
      //file already fetched
      if (!file.fileContent) {
        let fileContent = await getFileService(item.ServerRelativeUrl)
        file.fileContent = fileContent

        setFileList((prev) => prev.map((f) => (f.Id === item.Id ? file : f)))
      }
      if (supportDocumentTypeList.includes(getFileExtension(item.Name))) {
        setSelectedFile({
          fileName: removeGuidFromFileName(file.Name),
          fileContent: file.fileContent,
          fileInfo: file,
        })
      } else {
        fileDownload(file.fileContent, file.Name)
      }
      ui.setLoading(false)
    }
  }

  const handleOnClickPreviousFolder = async (item) => {
    let index = currentParentId.findIndex((f) => f.URL === item.URL)

    const updatedParentIds = currentParentId.slice(0, index + 1)
    setCurrentParentId(updatedParentIds)

    const data = await getItemsService(lists.StoreRecords, {
      filter: `ParentID eq ${item.Id}`,
    })

    // Cập nhật fileList với dữ liệu mới
    setFileList(data.value)
  }

  const handleSaveFolder = async (value) => {
    if (fileList.find((f) => removeGuidFromFileName(f.Name) === value.folderName)) {
      ui.notiError('Folder already exists')
      ui.setLoading(false)
      return
    }
    setIsSavingFolder(true)
    if (folderFormMode === 'New') {
      let folderURL = `${currentParentId[currentParentId.length - 1].URL}/${v4()}_${
        value.folderName
      }`
      folderURL = folderURL.replace('Store', '')

      const folderCreated = await addFolderService(folderURL)

      const data = await getItemsService(lists.StoreRecords, {
        filter: `ServerRelativeUrl eq '${folderCreated.serverRelativeUrl}'`,
      })
      await updateListItemService(lists.StoreRecords, data.value[0].Id, {
        RefID: storeID,
        DataSource: list.listName,
      })
      await setFileList((prev) => [
        { ...data.value[0], RefID: storeID, DataSource: list.listName },
        ...prev,
      ])
    } else {
      await updateListItemService(lists.StoreRecords, selectedFolder.Id, {
        Name: `${v4()}_${value.folderName}`,
      })
      const uploaded = await getItemsService(lists.StoreRecords, {
        filter: `Id eq ${selectedFolder.Id}`,
      })
      const newFileList = fileList.map((f) => {
        return f.Id === selectedFolder.Id ? uploaded.value[0] : f
      })
      setFileList(newFileList)
    }
    setIsShowSaveFolder(false)
    form.resetFields()

    setIsSavingFolder(false)
  }

  const handleOnClickRenameFolder = (item) => {
    setIsShowSaveFolder(true)
    setFolderFormMode('Edit')
    setSelectedFolder(item)
    form.setFieldsValue({
      folderName: removeGuidFromFileName(item.Name),
    })
  }

  useEffect(() => {
    handleGetFiles()
  }, [])
  return (
    <div>
      {selectedFile !== null && (
        <PreviewFile
          file={selectedFile}
          onCancel={() => setSelectedFile(null)}
          site={list.listName}
        />
      )}

      <div className="">
        <Dragger
          disabled={mode === 'View' || fileList.length >= maxCount || isUploading}
          fileList={[]}
          customRequest={handleUploadFile}
          beforeUpload={beforeUpload}
          accept={accept}>
          {fileList.length < maxCount ? (
            isUploading ? (
              <Image src={imgSpinner} preview={false} width={70} />
            ) : (
              <>
                <div className=" p-1 border-1 border-solid border-gray-400 ">
                  <FontAwesomeIcon icon={faUpload} className="text-xl text-[#5ac2dc]" />
                </div>
                <p className="mt-2">
                  <span className={`text-[#5ac2dc]`}>Click to Upload</span> or drag and drop
                </p>
                <p className="text-xs text-gray-400">
                  (Max. File size: {maxFileSize.size}
                  {maxFileSize.unit} {maxCount === Infinity ? '' : `| Max. File count: ${maxCount}`}
                  )
                </p>
              </>
            )
          ) : (
            <p className="text-xs text-red-500">Max file count reached</p>
          )}
        </Dragger>
      </div>
      <DocumentURl
        currentURL={currentParentId}
        handleOnClickPreviousFolder={handleOnClickPreviousFolder}
        setIsShowSaveFolder={setIsShowSaveFolder}
        setFolderFormMode={setFolderFormMode}
      />
      <div className="">
        {isGettingFiles || isUploading ? (
          <div className="d-flex flex-col justify-center items-center">
            <Image width={70} src={imgSpinner} preview={false} />
            <span>Loading data....</span>
          </div>
        ) : (
          fileList.map((item) => (
            <DocumentItem
              key={item.Id}
              item={item}
              handleOnClickFile={handleOnClickFile}
              handleOnClickRenameFolder={handleOnClickRenameFolder}
              setSelectedDeleteFile={setSelectedDeleteFile}
            />
          ))
        )}
      </div>
      <Modal
        title={<h5>Confirm delete</h5>}
        open={selectedDeleteFile !== null}
        onCancel={() => {
          if (!isDeletingItem) setSelectedDeleteFile(null)
        }}
        onOk={handleDeleteFile}
        okButtonProps={{
          icon: <i className="ms-1 fa-solid fa-trash text-white w-[22px] cursor-pointer "></i>,
          danger: true,
          loading: isDeletingItem,
        }}
        cancelButtonProps={{
          icon: <i className="fa-solid fa-xmark w-[25px] cursor-pointer "></i>,
        }}
        centered>
        <p className="py-3">Are you sure want to delete this file?</p>
      </Modal>

      <Modal
        title={<h1>Create a folder</h1>}
        open={isShowSaveFolder}
        cancelButtonProps={null}
        onCancel={() => {
          if (!isSavingFolder) setIsShowSaveFolder(false)
        }}
        footer={[]}
        centered>
        <Form
          form={form}
          layout="vertical"
          className="mt-3"
          onFinish={(value) => handleSaveFolder(value)}>
          <Form.Item
            name="folderName"
            label="Name"
            rules={[{ required: true, message: 'Please enter folder name' }]}>
            <Input />
          </Form.Item>
          <Form.Item hidden={true} name="item"></Form.Item>
          <div className="d-flex justify-end gap-2">
            <Button
              key="cancel"
              type="default"
              icon={<FontAwesomeIcon icon={faClose} />}
              onClick={() => {
                if (!isSavingFolder) setIsShowSaveFolder(false)
              }}>
              Cancel
            </Button>

            <Button
              key="add"
              icon={<FontAwesomeIcon icon={faSave} />}
              type="primary"
              htmlType="submit"
              loading={isSavingFolder}>
              Save
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  )
}

DocumentStore.propTypes = propTypes
export default DocumentStore
