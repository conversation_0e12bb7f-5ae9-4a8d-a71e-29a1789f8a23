import { useState, useEffect } from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  getAllBaseSalary45Months,
  getAllTblSsPolicyShortCodes,
  getApiPatientVisitPolicySsViewByPatientVisitId,
  getApiPolicySubscriptionSsViewByCardDateAndPatientId,
} from '../VisitService'
import { getSuggestedPoliciesForAllCards, processAndSortPoliciesByCoverage } from '../VisitHelpers'
import { calculateTotalForColumn, displayCurrency, handleError } from '../../../common/helpers'
import dayjs from '../../../common/dayjs'
import { POLICY_SUBSCRIPTION_STATUS, REFERRAL_DISPOSITION } from '../VisitConstant'
import { FORMAT_DATE } from '../../../common/constant'
import { subscribePolicyService } from '../../../common/services'
import { useUI } from '../../../common/UIProvider'
import { isEqual } from 'lodash'
import useDeepCompareEffect from 'use-deep-compare-effect'

// Keys for React Query
export const VISIT_POLICY_SUBSCRIPTION_QUERY_KEYS = {
  BASE_SALARY_45_MONTHS: 'baseSalary45Month',
  POLICY_SHORT_CODES: 'tblSsPolicyShortCodes',
  PATIENT_VISIT_POLICY_SUBSCRIPTION: 'patientVisitPolicySubscription',
  PATIENT_POLICY_SUBSCRIPTIONS: 'patientPolicySubscriptions',
}

export const usePolicySubscription = ({
  mainVisit,
  visitChargeDetailsData,
  healthInsuranceCards,
  enabled = true,
}) => {
  // Các state nội bộ
  const [suggestedPolicySubscriptions, setSuggestedPolicySubscriptions] = useState([])
  const [selectedPolicySubscription, setSelectedPolicySubscription] = useState(null)
  const [responseSubscription, setResponseSubscription] = useState({
    message: null,
    status: null,
    defaultPolicyId: null,
    defaultPolicyShortCode: null,
  })
  const ui = useUI()
  const queryClient = useQueryClient()

  // Query lấy 45 tháng lương cơ sở
  const baseSalary45MonthDataQuery = useQuery({
    queryKey: [VISIT_POLICY_SUBSCRIPTION_QUERY_KEYS.BASE_SALARY_45_MONTHS],
    queryFn: async () => getAllBaseSalary45Months().then((res) => res.value[0]),
    enabled: enabled,
  })

  const baseSalary45Month = baseSalary45MonthDataQuery.data || {}

  // Query lấy danh sách policy short codes (đã được xử lý sort theo coverage)
  const tblSsPolicyShortCodesQuery = useQuery({
    queryKey: [VISIT_POLICY_SUBSCRIPTION_QUERY_KEYS.POLICY_SHORT_CODES],
    queryFn: async () =>
      getAllTblSsPolicyShortCodes().then((res) => processAndSortPoliciesByCoverage(res.value)),
    enabled: enabled,
  })

  const tblSsPolicyShortCodes = tblSsPolicyShortCodesQuery.data || []

  // Lọc các thẻ BHYT hợp lệ
  const validHealthInsuranceCards = healthInsuranceCards //filterValidHealthInsuranceCards(healthInsuranceCards) || []

  // Query lấy patient visit policy subscription (policy của lượt khám)
  const patientVisitPolicySubscriptionQuery = useQuery({
    queryKey: [
      VISIT_POLICY_SUBSCRIPTION_QUERY_KEYS.PATIENT_VISIT_POLICY_SUBSCRIPTION,
      mainVisit?.patient_visit_id,
    ],
    queryFn: async () => {
      const response = await getApiPatientVisitPolicySsViewByPatientVisitId(
        mainVisit.patient_visit_id,
      )
      const patientVisitPolicySubscription = response?.value?.[0] || null

      return patientVisitPolicySubscription // Ensure a valid value is returned
    },
    enabled: !!mainVisit?.patient_visit_id && enabled,
  })

  const patientVisitPolicySubscription = patientVisitPolicySubscriptionQuery.data || {}

  // Query lấy patient policy subscriptions (policy của bệnh nhân)
  const patientPolicySubscriptionsQuery = useQuery({
    queryKey: [
      VISIT_POLICY_SUBSCRIPTION_QUERY_KEYS.PATIENT_POLICY_SUBSCRIPTIONS,
      mainVisit?.patient_id,
    ],
    queryFn: async () => {
      const tempPatientPolicySubscriptions = await Promise.all(
        validHealthInsuranceCards.map((card) =>
          getApiPolicySubscriptionSsViewByCardDateAndPatientId(
            mainVisit?.patient_id,
            dayjs(card?.effective_date).format(FORMAT_DATE),
            dayjs(card?.expiration_date).format(FORMAT_DATE),
          ).then((res) => {
            // Nếu API trả về kết quả, thêm trường card_code từ card
            if (res?.value?.[0]) {
              return { ...res.value[0], card_code: card.card_code }
            }
            return undefined // Trả về undefined nếu không có kết quả
          }),
        ),
      ).then((results) => results.filter((item) => item !== undefined))

      return processAndSortPoliciesByCoverage(tempPatientPolicySubscriptions)
    },
    enabled: !!mainVisit?.patient_id && enabled,
  })

  const patientPolicySubscriptions = patientPolicySubscriptionsQuery.data || {}

  // Check if any query is loading
  const isLoading =
    patientVisitPolicySubscriptionQuery.isLoading ||
    patientPolicySubscriptionsQuery.isLoading ||
    baseSalary45MonthDataQuery.isLoading ||
    tblSsPolicyShortCodesQuery.isLoading

  // Khi các dữ liệu cần thiết đã có, tính toán các giá trị và set state
  useDeepCompareEffect(() => {
    if (
      visitChargeDetailsData &&
      validHealthInsuranceCards &&
      tblSsPolicyShortCodes.length > 0 &&
      patientPolicySubscriptions.length >= 0 &&
      !isLoading
    ) {
      // Tính tổng tiền BHYT từ các dòng có manual_ss_cover_flag === true
      const healthInsuranceAmountTotal = calculateTotalForColumn(
        visitChargeDetailsData.filter((item) => item.manual_ss_cover_flag === true),
        'health_insurance_amount',
      )

      // Kiểm tra xem tổng tiền BHYT có nhỏ hơn 15% lương cơ sở không
      const isTotalLessThanBase15PercentSalary =
        healthInsuranceAmountTotal > 0 &&
        healthInsuranceAmountTotal < baseSalary45Month?.base_15_percent_salary &&
        !mainVisit?.treatment_course_flag // [434] theo đợt điều trị không áp dụng

      // Tính ra danh sách policy gợi ý dựa trên các đầu vào:
      // mainVisit, validHealthInsuranceCards, tblSsPolicyShortCodes,
      // điều kiện tổng tiền BHYT nhỏ hơn 15% lương cơ sở, và patientPolicySubscriptions.
      const suggestedSubs = getSuggestedPoliciesForAllCards(
        mainVisit,
        validHealthInsuranceCards,
        tblSsPolicyShortCodes,
        isTotalLessThanBase15PercentSalary,
        patientPolicySubscriptions,
      )

      if (!isEqual(suggestedPolicySubscriptions, suggestedSubs)) {
        setSuggestedPolicySubscriptions(suggestedSubs)
      }

      // --- TRƯỜNG HỢP 0: Nếu policy lượt khám trùng với policy gợi ý ---
      // Nếu policy của lượt khám (patientVisitPolicySubscription) có short_code bằng policy gợi ý (suggestedSubs[0])
      if (
        patientVisitPolicySubscription?.short_code == suggestedSubs[0]?.short_code &&
        patientVisitPolicySubscription?.short_code != null
      ) {
        const newResponse = {
          message: 'This patient has already been subscribed',
          status: POLICY_SUBSCRIPTION_STATUS.SUBSCRIBED.name_e,
          defaultPolicyId: null,
          defaultPolicyShortCode: null,
          defaultPolicyName: null,
        }
        const newSelectedPolicy = suggestedSubs[0]

        if (!isEqual(responseSubscription, newResponse)) {
          setResponseSubscription(newResponse)
        }
        if (!isEqual(selectedPolicySubscription, newSelectedPolicy)) {
          setSelectedPolicySubscription(newSelectedPolicy)
        }

        return
      }

      // --- TRƯỜNG HỢP 1: Nếu policy của bệnh nhân khác với policy gợi ý ---
      if (patientPolicySubscriptions[0]?.short_code !== suggestedSubs[0]?.short_code) {
        // Case 1.a: Nếu bệnh nhân đã chọn Free Copay
        if (mainVisit?.free_copay_flag) {
          const newResponse = {
            message:
              'Bệnh nhân đã được chọn hưởng miễn đồng chi trả, nhấn ok để subscribe bệnh nhân vào policy 100%',
            status: POLICY_SUBSCRIPTION_STATUS.NEED_SUBSCRIBE.name_e,
            defaultPolicyId: suggestedSubs[0]?.policy_id,
            defaultPolicyShortCode: suggestedSubs[0]?.short_code,
            defaultPolicyName: suggestedSubs[0]?.name_e,
          }
          const newSelectedPolicy = suggestedSubs[0]

          if (!isEqual(responseSubscription, newResponse)) {
            setResponseSubscription(newResponse)
          }
          if (!isEqual(selectedPolicySubscription, newSelectedPolicy)) {
            setSelectedPolicySubscription(newSelectedPolicy)
          }

          return
        }
        // Case 1.b: Nếu tổng tiền BHYT <= 15% lương cơ sở
        else if (isTotalLessThanBase15PercentSalary) {
          const newResponse = {
            message: `Bệnh nhân đang có Tổng tiền BHYT (${displayCurrency(
              healthInsuranceAmountTotal,
            )}) <= 15% 1 tháng lương cơ sở (${displayCurrency(
              baseSalary45Month?.base_15_percent_salary,
            )}), nhấn ok để subscribe bệnh nhân vào policy 100%`,
            status: POLICY_SUBSCRIPTION_STATUS.NEED_SUBSCRIBE.name_e,
            defaultPolicyId: suggestedSubs[0]?.policy_id,
            defaultPolicyShortCode: suggestedSubs[0]?.short_code,
            defaultPolicyName: suggestedSubs[0]?.name_e,
          }
          const newSelectedPolicy = suggestedSubs[0]

          if (!isEqual(responseSubscription, newResponse)) {
            setResponseSubscription(newResponse)
          }
          if (!isEqual(selectedPolicySubscription, newSelectedPolicy)) {
            setSelectedPolicySubscription(newSelectedPolicy)
          }

          return
        }
        // Case 1.c: Nếu nơi đăng kí khám ban đầu là FV
        else if (
          suggestedSubs[0]?.referral_disposition_rcd ===
          REFERRAL_DISPOSITION.FV_REGISTER_REFERRAL_DISPOTITION_CODE
        ) {
          const newResponse = {
            message:
              'Bệnh nhân đã đăng kí nơi khám chữa bệnh ban đầu là FV, nhấn ok để subscribe bệnh nhân vào policy 100%',
            status: POLICY_SUBSCRIPTION_STATUS.NEED_SUBSCRIBE.name_e,
            defaultPolicyId: suggestedSubs[0]?.policy_id,
            defaultPolicyShortCode: suggestedSubs[0]?.short_code,
            defaultPolicyName: suggestedSubs[0]?.name_e,
          }
          const newSelectedPolicy = suggestedSubs[0]

          if (!isEqual(responseSubscription, newResponse)) {
            setResponseSubscription(newResponse)
          }
          if (!isEqual(selectedPolicySubscription, newSelectedPolicy)) {
            setSelectedPolicySubscription(newSelectedPolicy)
          }

          return
        }
        // Case 1.d: Các trường hợp còn lại => cho rằng bệnh nhân chưa subscribe vào policy theo mức gợi ý
        else {
          const newResponse = {
            message: `Bệnh nhân chưa subscribe vào policy ${suggestedSubs[0]?.name_e}, nhấn ok để subscribe`,
            status: POLICY_SUBSCRIPTION_STATUS.NEED_SUBSCRIBE.name_e,
            defaultPolicyId: suggestedSubs[0]?.policy_id,
            defaultPolicyShortCode: suggestedSubs[0]?.short_code,
            defaultPolicyName: suggestedSubs[0]?.name_e,
          }
          const newSelectedPolicy = suggestedSubs[0]

          if (!isEqual(responseSubscription, newResponse)) {
            setResponseSubscription(newResponse)
          }
          if (!isEqual(selectedPolicySubscription, newSelectedPolicy)) {
            setSelectedPolicySubscription(newSelectedPolicy)
          }

          return
        }
      }

      // --- TRƯỜNG HỢP 2: Nếu policy của bệnh nhân đã subscribe trùng với policy gợi ý nhưng khác với policy lượt khám ---
      if (
        patientPolicySubscriptions[0]?.short_code === suggestedSubs[0]?.short_code &&
        suggestedSubs[0]?.short_code != null &&
        patientPolicySubscriptions[0]?.short_code !== patientVisitPolicySubscription?.short_code
      ) {
        const newResponse = {
          message: `Bệnh nhân đã được subscribe vào policy ${suggestedSubs[0]?.name_e} nhưng chưa được subscribe vào visit, nhấn ok để đóng`,
          status: POLICY_SUBSCRIPTION_STATUS.WARNING.name_e,
          defaultPolicyId: null,
          defaultPolicyShortCode: null,
          defaultPolicyName: null,
        }
        const newSelectedPolicy = suggestedSubs[0]

        if (!isEqual(responseSubscription, newResponse)) {
          setResponseSubscription(newResponse)
        }
        if (!isEqual(selectedPolicySubscription, newSelectedPolicy)) {
          setSelectedPolicySubscription(newSelectedPolicy)
        }

        return
      }

      // --- TRƯỜNG HỢP 3: Nếu bệnh nhân không trình thẻ BHYT ---
      if (validHealthInsuranceCards.length === 0) {
        const newResponse = {
          message: 'Bệnh nhân chưa trình thẻ BHYT, vui lòng cập nhật thẻ để subscribe policy',
          status: POLICY_SUBSCRIPTION_STATUS.FAILED.name_e,
          defaultPolicyId: null,
          defaultPolicyShortCode: null,
          defaultPolicyName: null,
        }
        const newSelectedPolicy = suggestedSubs[0]

        if (!isEqual(responseSubscription, newResponse)) {
          setResponseSubscription(newResponse)
        }
        if (!isEqual(selectedPolicySubscription, newSelectedPolicy)) {
          setSelectedPolicySubscription(newSelectedPolicy)
        }

        return
      }

      // --- Default Case: Nếu không rơi vào các trường hợp trên ---
      const newResponse = {
        message: 'This patient has already been subscribed',
        status: POLICY_SUBSCRIPTION_STATUS.SUBSCRIBED.name_e,
        defaultPolicyId: null,
        defaultPolicyShortCode: null,
        defaultPolicyName: null,
      }
      const newSelectedPolicy = suggestedSubs[0]

      if (!isEqual(responseSubscription, newResponse)) {
        setResponseSubscription(newResponse)
      }
      if (!isEqual(selectedPolicySubscription, newSelectedPolicy)) {
        setSelectedPolicySubscription(newSelectedPolicy)
      }

      return
    }
  }, [
    mainVisit,
    visitChargeDetailsData,
    validHealthInsuranceCards,
    tblSsPolicyShortCodes,
    patientPolicySubscriptions,
    patientVisitPolicySubscription,
    baseSalary45Month,
    isLoading,
  ])

  const filteredTblSsPolicyShortCodes = tblSsPolicyShortCodes.filter(
    (policy) => policy.pricing_class === mainVisit?.pricing_class,
  )

  // Mutation để xử lý việc subscribe policy
  const subscribePolicyMutation = useMutation({
    mutationFn: async ({
      patientId,
      policyId,
      startDate = '2024-01-11',
      endDate = '2025-01-10',
    }) => {
      const req = {
        patient_id: patientId,
        policy_id: policyId,
        start_date: startDate, //2024-01-11
        end_date: endDate, //2025-01-10
      }
      const response = await subscribePolicyService(req)
      return response
    },
    onSuccess: async () => {
      // Xử lý khi mutation thành công
      await refetchData()

      ui.notiSuccess('Subscribe policy cho bệnh nhân thành công')
    },
    onError: () => {
      // Xử lý khi mutation thất bại
      ui.notiError('Subscribe policy thất bại')
    },
  })

  // Hàm refresh để refetch tất cả các query liên quan
  // Manually refetch data
  const refetchData = () => {
    queryClient.invalidateQueries({
      queryKey: [
        VISIT_POLICY_SUBSCRIPTION_QUERY_KEYS.PATIENT_VISIT_POLICY_SUBSCRIPTION,
        mainVisit?.patient_visit_id,
      ],
    })
    queryClient.invalidateQueries({
      queryKey: [
        VISIT_POLICY_SUBSCRIPTION_QUERY_KEYS.PATIENT_POLICY_SUBSCRIPTIONS,
        mainVisit?.patient_id,
      ],
    })
  }

  return {
    suggestedPolicySubscriptions,
    responseSubscription,
    patientPolicySubscriptions,
    patientVisitPolicySubscription,
    filteredTblSsPolicyShortCodes,
    selectedPolicySubscription,
    setSelectedPolicySubscription,
    setResponseSubscription,
    subscribePolicyMutation,
    refetchData,
    isLoading,
  }
}
