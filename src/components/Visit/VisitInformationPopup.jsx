import { DatePicker, Form, Input, Modal, Select } from 'antd'
import React, { memo, useCallback, useEffect, useState } from 'react'
import PropTypes from '../../common/PropTypes'
import COLOR from '../../common/color'
import dayjs from '../../common/dayjs'
import lists from '../../common/lists'
import { updateListItemService } from '../../common/services'
import { useUI } from '../../common/UIProvider'
import { displayDate, displayDateTime, filterOption, handleError } from '../../common/helpers'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'
import { ACTION_VISIT_HISTORY } from './VisitConstant'
import { usePatientVisitHistory } from './hooks/usePatientVisitHistory'
import { FORMAT_DATETIME } from '../../common/constant'
import { max, min } from 'lodash'
import { PERMISSION } from '../Auth/AuthConstant'
import { useAuth } from '../../common/AuthProvider'
import { useDepartmentMapping } from '../../queryHooks/useDepartmentMapping'

const propTypes = {
  isVisibleVisitInformationPopup: PropTypes.bool.isRequired,
  setIsVisibleVisitInformationPopup: PropTypes.func.isRequired,
  refetchPatientVisit: PropTypes.func.isRequired,
  mainVisit: PropTypes.object.isRequired,
}

const KET_QUA_DTRI_LIST = [
  { KET_QUA_DTRI: '1', description: '1. Khỏi' },
  { KET_QUA_DTRI: '2', description: '2. Đỡ' },
  { KET_QUA_DTRI: '3', description: '3. Không thay đổi' },
  { KET_QUA_DTRI: '4', description: '4. Nặng hơn' },
  { KET_QUA_DTRI: '5', description: '5. Tử vong' },
  { KET_QUA_DTRI: '6', description: '6. Tiên lượng nặng xin về' },
  { KET_QUA_DTRI: '7', description: '7. Chưa xác định' },
]

const MA_LOAI_RV_LIST = [
  { MA_LOAI_RV: '1', description: '1. Ra viện' },
  { MA_LOAI_RV: '2', description: '2. Chuyển tuyến theo yêu cầu chuyên môn' },
  { MA_LOAI_RV: '3', description: '3. Trốn viện' },
  { MA_LOAI_RV: '4', description: '4. Xin ra viện' },
  { MA_LOAI_RV: '5', description: '5. Chuyến tuyến theo yêu cầu người bệnh' },
]

function VisitInformationPopup({
  isVisibleVisitInformationPopup,
  setIsVisibleVisitInformationPopup,
  refetchPatientVisit,
  mainVisit,
  currentPatientVisitMappingViews,
}) {
  const { currentUser } = useSelector((MODULE) => MODULE[MODULE_AUTH])

  const { checkPermission } = useAuth()
  const { departmentSS } = useDepartmentMapping()

  const [form] = Form.useForm()

  const ui = useUI()

  const { addPatientVisitHistory } = usePatientVisitHistory()

  const [suggestNGAY_RA, setSuggestNGAY_RA] = useState(null)

  const init = async () => {
    if (mainVisit.patient_visit_id) {
      try {
        const arrayDate = [
          ...currentPatientVisitMappingViews.map((item) =>
            item.NGAY_RA ? dayjs(item.NGAY_RA) : null,
          ),
          ...currentPatientVisitMappingViews.map((item) =>
            item.creation_date_time ? dayjs(item.creation_date_time) : null,
          ),
        ]

        const NGAY_RA = max(arrayDate) || dayjs()

        setSuggestNGAY_RA(NGAY_RA)

        form.setFieldsValue({
          ...mainVisit,
          NGAY_RA: NGAY_RA,
          NGAY_TTOAN: mainVisit.NGAY_TTOAN ? dayjs(mainVisit.NGAY_TTOAN) : NGAY_RA,
        })
      } catch (error) {
        handleError(error, 'VisitInformationPopup>init')
      }
    }
  }

  const handleUpdateVisitInformation = async () => {
    try {
      await form.validateFields()
    } catch (error) {
      return
    }

    try {
      const values = form.getFieldValue()
      const ketQuaDtri = KET_QUA_DTRI_LIST.find((item) => item.KET_QUA_DTRI === values.KET_QUA_DTRI)
      const maLoaiRV = MA_LOAI_RV_LIST.find((item) => item.MA_LOAI_RV === values.MA_LOAI_RV)

      const newValue = {
        NGAY_RA: values.NGAY_RA,
        NGAY_TTOAN: values.NGAY_TTOAN,
        TEN_BS: values.TEN_BS,
        MA_BS: values.MA_BS,
        KET_QUA_DTRI: values.KET_QUA_DTRI,
        KET_QUA_DTRI_DESCRIPTION: ketQuaDtri?.description,
        MA_LOAI_RV: values.MA_LOAI_RV,
        MA_LOAI_RV_DESCRIPTION: maLoaiRV?.description,
        MA_KHOA_RV: values.MA_KHOA_RV,
      }

      await Promise.all([
        updateListItemService(lists.patient_visit, mainVisit.patient_visit_id, newValue),
      ])

      const modifiedFields = []
      Object.keys(newValue).forEach((key) => {
        if (newValue[key] !== mainVisit[key]) {
          modifiedFields.push(`${key} ${mainVisit[key]} -> ${values[key]}`)
        }
      })

      const historyData = {
        patient_visit_id: mainVisit?.patient_visit_id,
        note: modifiedFields.join('\n'),
      }

      addPatientVisitHistory.mutateAsync({
        historyData,
        action: ACTION_VISIT_HISTORY.SAVE_INFO,
      })

      await refetchPatientVisit()
      setIsVisibleVisitInformationPopup(false)
      ui.notiSuccess('Cập nhật thông tin lượt khám thành công.')
    } catch (error) {
      handleError(error)
    }
  }

  useEffect(() => {
    init()
  }, [mainVisit])

  return (
    <Form form={form} layout="vertical">
      <Modal
        className="custom-modal"
        width={2000}
        title="Thông tin lượt khám"
        centered
        open={isVisibleVisitInformationPopup}
        destroyOnClose
        onClose={() => {
          setIsVisibleVisitInformationPopup(false)
        }}
        onCancel={() => {
          setIsVisibleVisitInformationPopup(false)
        }}
        onOk={handleUpdateVisitInformation}>
        <div className="flex flex-col gap-3">
          <div className="font-bold" style={{ color: COLOR.cyan }}>
            Thông tin lượt khám
          </div>

          <Form.Item
            name="NGAY_RA"
            label="Ngày ra"
            extra={
              suggestNGAY_RA
                ? 'Ngày ra hóa đơn gần nhất: ' + displayDateTime(suggestNGAY_RA, FORMAT_DATETIME)
                : ''
            }>
            <DatePicker showTime className="w-100" format={FORMAT_DATETIME} />
          </Form.Item>

          <Form.Item
            name="NGAY_TTOAN"
            label="NGAY_TTOAN (XML1)"
            rules={[
              {
                validator: () => {
                  // NGAY_TTOAN phải >= NGAY_RA
                  const NGAY_RA = form.getFieldValue('NGAY_RA')
                  const NGAY_TTOAN = form.getFieldValue('NGAY_TTOAN')

                  if (NGAY_TTOAN && NGAY_RA && NGAY_TTOAN.isBefore(NGAY_RA)) {
                    return Promise.reject(new Error('NGAY_TTOAN phải >= NGAY_RA'))
                  }

                  return Promise.resolve()
                },
              },
            ]}>
            <DatePicker
              disabled={!checkPermission(PERMISSION.SIO_MANAGER)}
              showTime
              className="w-100"
              format={FORMAT_DATETIME}
            />
          </Form.Item>

          <Form.Item name="TEN_BS" label="Tên bác sĩ (XML7)">
            <Input />
          </Form.Item>
          <Form.Item name="MA_BS" label="Mã bác sĩ (XML7)">
            <Input />
          </Form.Item>
          <Form.Item name="KET_QUA_DTRI" label="Kết quả điều trị (XML1)">
            <Select>
              {KET_QUA_DTRI_LIST.map((item) => (
                <Select.Option key={item.KET_QUA_DTRI} value={item.KET_QUA_DTRI}>
                  {item.description}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="MA_LOAI_RV" label="Mã loại ra viện (XML1)">
            <Select>
              {MA_LOAI_RV_LIST.map((item) => (
                <Select.Option key={item.MA_LOAI_RV} value={item.MA_LOAI_RV}>
                  {item.description}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="MA_KHOA_RV" label="MA_KHOA_RV (XML7)">
            <Select
              allowClear
              showSearch
              filterOption={filterOption}
              optionLabelProp="label"
              disabled={!checkPermission(PERMISSION.SIO_MANAGER)}>
              {departmentSS.map((dept) => (
                <Select.Option
                  key={dept.ma_khoa_bhyt + dept.ten_khoa_bhyt}
                  value={dept.ma_khoa_bhyt}
                  label={dept.ma_khoa_bhyt}>
                  {dept.ma_khoa_bhyt} - {dept.ten_khoa_bhyt}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </div>
      </Modal>
    </Form>
  )
}
VisitInformationPopup.propTypes = propTypes
export default memo(VisitInformationPopup)
