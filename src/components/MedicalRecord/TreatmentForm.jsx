import { Button, Form, Input, Modal, Select, Switch, Tag } from 'antd'
import React, { useEffect, useState } from 'react'
import { useForm } from 'antd/es/form/Form'
import { useVisitChargeDetails } from '../Visit/hooks/useVisitChargeDetails'
import useDeepCompareEffect from 'use-deep-compare-effect'
import { useMedicalRecord } from '../../queryHooks/useMedicalRecord'
import { handleError } from '../../common/helpers'
import { getItemsService, updateListItemService } from '../../common/services'
import lists from '../../common/lists'
import AsyncButton from '../../common/components/AsyncButton'
import { FORM_MODE } from '../../common/constant'
import MedicalRecordField, { MEDICAL_RECORD_FIELD_TYPE } from './MedicalRecordField'
import useApp from 'antd/es/app/useApp'
import DocumentStore from '../../common/components/DocumentStore/DocumentStore'
import PdfViewer from '../../common/components/PdfViewer'
import {
  cdhaResultOptions,
  conclusionOptions,
  testResultOptions,
  treatmentOptions,
  treatmentResponseOptions,
  visitReasonOptions,
} from './MedicalRecordConstant'
import dayjs from '../../common/dayjs'
import { usePatientMedicalCoding } from '../Visit/hooks/usePatientMedicalCoding'
import { useChemoPrescription } from '../../queryHooks/useChemoPrescription'
import { useGeneralPrescription } from '../../queryHooks/useGeneralPrescription'
import {
  formatChemoPrescriptionDetail,
  formatGeneralPrescriptionDetail,
} from './MedicalRecordService'
import { useDepartmentMapping } from '../../queryHooks/useDepartmentMapping'
import { handlePrintPDF } from '../../SI/helper'
import { useAllChargeDetails } from '../../queryHooks/useAllChargeDetails'
import LazySelect from '../../common/components/LazySelect'

const borderTable = {
  border: '1px solid gray',
  padding: 5,
  verticalAlign: 'top',
}

const TreatmentForm = ({ selectedVisit, mainVisit, selectedMedicalRecordId }) => {
  const [form] = useForm()
  const app = useApp()

  // hooks
  const { data: visitChargeDetailsData } = useVisitChargeDetails(selectedVisit)
  const { medicalRecordDetail, refetchMedicalRecordDetail } = useMedicalRecord({
    medicalRecordFormId: selectedMedicalRecordId,
  })
  const { patientMedicalCodingList } = usePatientMedicalCoding(selectedVisit?.patient_visit_id)
  const { departmentMappings } = useDepartmentMapping()
  // const { coveredChargeDetails } = useAllChargeDetails(mainVisit?.patient_visit_id)

  // prescription
  const {
    chemoPrescriptionDetail,
    refetchAll: refetchChemoPrescription,
    isChemoPrescriptionFetching,
  } = useChemoPrescription({
    patientVisitId: selectedVisit?.patient_visit_id,
    byDate: medicalRecordDetail?.form_date_time,
    byEmployeeId: medicalRecordDetail?.employee_id,
  })

  const {
    generalPrescriptionDetail,
    refetchAll: refetchGeneralPrescription,
    isGeneralPrescriptionFetching,
  } = useGeneralPrescription({
    patientVisitId: selectedVisit?.patient_visit_id,
    byDate: medicalRecordDetail?.form_date_time,
    byEmployeeId: medicalRecordDetail?.employee_id,
  })

  const [formMode, setFormMode] = useState(FORM_MODE.view)
  const isEdit = formMode === FORM_MODE.edit
  const [openDocumentStore, setOpenDocumentStore] = useState(false)
  const [attachments, setAttachments] = useState([])
  const [viewFileOnly, setViewFileOnly] = useState()
  const [chargeConclusion, setChargeConclusion] = useState('')
  const [chargeTestDescription, setChargeTestDescription] = useState('')
  const [chargeCdhaDescription, setChargeCdhaDescription] = useState('')

  // employee states
  const [openChangeEmployee, setOpenChangeEmployee] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState()

  // prescription states
  const [resetMedicineDescription, setResetMedicineDescription] = useState(false)
  const [resetPrescription, setResetPrescription] = useState(false)

  // default values
  useEffect(() => {
    const defaultDiagnosis =
      medicalRecordDetail?.diagnosis ||
      patientMedicalCodingList
        .map((coding) => coding.TEN_BENH_CHINH || coding.TEN_BENH_KT)
        .join('; ') ||
      ''

    form.setFieldsValue({
      room: medicalRecordDetail?.room, // Buồng
      bed: medicalRecordDetail?.bed, // Giường
      visitReason: medicalRecordDetail?.visit_reason, // Lý do khám bệnh
      treatmentResponse: medicalRecordDetail?.treatment_response, // Đáp ứng điều trị
      testResult: medicalRecordDetail?.test_result, // Kết quả xét nghiệm
      cdhaResult: medicalRecordDetail?.cdha_result, // Kết quả CĐHA
      disease_progression: medicalRecordDetail?.disease_progression, // Giai đoạn bệnh
      conclusion: medicalRecordDetail?.conclusion, // Kết luận
      treatment: medicalRecordDetail?.treatment, // Điều trị
      testDescription: medicalRecordDetail?.test_description, // Xét nghiệm
      cdhaDescription: medicalRecordDetail?.cdha_description, // CDHA
      chemotherapyCycle: medicalRecordDetail?.chemotherapy_cycle || 'HÓA TRỊ ĐỢT ', // Hóa trị đợt
      medicineDescription: medicalRecordDetail?.medicine_description, // Hóa trị đợt
      prescription: medicalRecordDetail?.prescription, // Đơn thuốc về
      nextVisit: medicalRecordDetail?.next_visit, // Hẹn lần khám tới
      note: medicalRecordDetail?.note, // Ghi chú
      departmentId: medicalRecordDetail?.department_id || selectedVisit?.department_id || '', // Khoa
      diagnosis: defaultDiagnosis,
    })

    // default employee
    setSelectedEmployee({
      employee_id: medicalRecordDetail?.employee_id,
      employee_name: medicalRecordDetail?.chairperson,
    })
  }, [medicalRecordDetail, selectedVisit])

  const therapyType = {
    chemotherapy: { key: 'chemotherapy', name: 'Hóa trị' },
    radiation: { key: 'radiation', name: 'Xạ trị' },
  }

  const isTherapyItem = (charge) => {
    return checkTherapyItem(charge).isTherapyItem
  }

  const checkTherapyItem = (charge) => {
    if (charge.health_insurance_name?.toLowerCase()?.includes('hóa trị')) {
      return {
        therapyType: therapyType.chemotherapy,
        isTherapyItem: true,
      }
    }

    if (charge.health_insurance_name?.toLowerCase()?.includes('xạ trị')) {
      return {
        therapyType: therapyType.radiation,
        isTherapyItem: true,
      }
    }

    return {
      therapyType: '',
      isTherapyItem: false,
    }
  }

  // auto fill chemotherapyCycle
  // useEffect(() => {
  //   if (!coveredChargeDetails?.[0] || !selectedVisit?.patient_visit_id) {
  //     return
  //   }

  //   // charge của đợt hóa trị/xạ trị hiện tại
  //   const currentCharge = coveredChargeDetails.find((charge) => {
  //     return charge.patient_visit_id === selectedVisit?.patient_visit_id && isTherapyItem(charge)
  //   })

  //   if (!currentCharge) {
  //     return
  //   }

  //   const currentTherapyType = checkTherapyItem(currentCharge).therapyType

  //   const beforeChargeList = coveredChargeDetails.filter((charge) => {
  //     return (
  //       charge.patient_visit_id !== selectedVisit?.patient_visit_id &&
  //       isTherapyItem(charge) &&
  //       checkTherapyItem(charge).therapyType.key === currentTherapyType.key &&
  //       dayjs(charge.charged_date_time) < dayjs(currentCharge?.charged_date_time)
  //     )
  //   })
  //   const newChemotherapyCycle =
  //     `${currentTherapyType.name} ĐỢT ${beforeChargeList.length + 1}`.toUpperCase()

  //   form.setFieldsValue({
  //     chemotherapyCycle: medicalRecordDetail?.chemotherapy_cycle || newChemotherapyCycle,
  //     nextVisit:
  //       medicalRecordDetail?.next_visit ||
  //       `Hẹn tái khám ${newChemotherapyCycle.toLowerCase()} + ngày ...`,
  //   })
  // }, [coveredChargeDetails, selectedVisit?.patient_visit_id])

  // auto fill testDescription and cdhaDescription
  useDeepCompareEffect(() => {
    if (!visitChargeDetailsData[0]) {
      return
    }

    const ssCoveredItemList = visitChargeDetailsData.filter((item) => item.manual_ss_cover_flag)

    // IPD243472
    const testDescription = ssCoveredItemList
      .map((item) =>
        item.ss_item_group_rcd == '1' ? item.health_insurance_name || item.item_name_e : '',
      )
      .filter(Boolean)
      .join(', ')
    setChargeTestDescription(testDescription)

    // OPD4007988
    const cdhaDescription = ssCoveredItemList
      .map((item) =>
        item.ss_item_group_rcd == '2' ? item.health_insurance_name || item.item_name_e : '',
      )
      .filter(Boolean)
      .join(', ')
    setChargeCdhaDescription(cdhaDescription)

    const chargeConclusion = ssCoveredItemList
      .map((item) => item.KET_LUAN || '')
      .filter(Boolean)
      .join('\n')
    setChargeConclusion(chargeConclusion)

    form.setFieldsValue({
      testDescription: form.getFieldValue('testDescription') || testDescription,
      cdhaDescription: form.getFieldValue('cdhaDescription') || cdhaDescription,
    })
  }, [visitChargeDetailsData])

  // auto fill prescription, only run when prescription is null
  useDeepCompareEffect(() => {
    if (!chemoPrescriptionDetail.length || !resetMedicineDescription) {
      return
    }

    form.setFieldsValue({
      medicineDescription: formatChemoPrescriptionDetail(chemoPrescriptionDetail),
    })

    setResetMedicineDescription(false)
  }, [chemoPrescriptionDetail, resetMedicineDescription])

  // auto fill prescription
  useDeepCompareEffect(() => {
    if (!generalPrescriptionDetail.length || !resetPrescription) {
      return
    }

    form.setFieldsValue({
      prescription: formatGeneralPrescriptionDetail(generalPrescriptionDetail),
    })

    setResetPrescription(false)
  }, [generalPrescriptionDetail, resetPrescription])

  useEffect(() => {
    setViewFileOnly(!!attachments[0] && !isEdit)
  }, [attachments, isEdit])

  const handleSaveMedicalRecordForm = async () => {
    try {
      await form.validateFields()
    } catch (error) {
      app.message.error('Vui lòng nhập thông tin bắt buộc!')
      return
    }

    const values = form.getFieldsValue()

    try {
      const newRecord = {
        room: values.room,
        bed: values.bed,
        visit_reason: values.visitReason,
        treatment_response: values.treatmentResponse,
        test_result: values.testResult,
        cdha_result: values.cdhaResult,
        disease_progression: values.disease_progression,
        conclusion: values.conclusion,
        treatment: values.treatment,
        test_description: values.testDescription,
        cdha_description: values.cdhaDescription,
        chemotherapy_cycle: values.chemotherapyCycle,
        medicine_description: values.medicineDescription,
        prescription: values.prescription,
        next_visit: values.nextVisit,
        note: values.note,
        department_id: values.departmentId,
        diagnosis: values.diagnosis,
        // employee
        employee_id: selectedEmployee?.employee_id,
        chairperson: selectedEmployee?.employee_name,
      }

      await updateListItemService(lists.medical_record_form, selectedMedicalRecordId, newRecord)

      refetchMedicalRecordDetail()

      app.message.success('Lưu thành công')
    } catch (error) {
      handleError(error, 'handleSaveMedicalRecordForm')
    }
  }

  return (
    <>
      <div>
        <div
          className="sticky-top d-flex justify-content-end align-items-center gap-2 pb-2"
          style={{ top: 105 }}>
          <div>
            <Tag
              onClick={() => setOpenChangeEmployee(true)}
              title={medicalRecordDetail?.employee_id}>
              {medicalRecordDetail?.chairperson}
            </Tag>
          </div>
          {/* toggle to view file or form */}
          <div className="d-flex align-items-center me-2 gap-2">
            <Switch onChange={() => setViewFileOnly(!viewFileOnly)} checked={viewFileOnly} /> Chỉ
            xem File đính kèm
          </div>
          <Button
            variant={isEdit ? 'outlined' : 'solid'}
            color="blue"
            icon={<i className="fa fa-edit" />}
            onClick={() => setFormMode(isEdit ? FORM_MODE.view : FORM_MODE.edit)}>
            {isEdit ? 'Tắt Chỉnh sửa' : 'Chỉnh sửa'}
          </Button>
          <AsyncButton
            icon={<i className="fa fa-save" />}
            hidden={!isEdit}
            onClick={handleSaveMedicalRecordForm}>
            Lưu
          </AsyncButton>
          <Button
            type="primary"
            style={{ background: '#2C9538' }}
            color="danger"
            icon={
              openDocumentStore ? <i className="fa fa-close" /> : <i className="fa fa-upload" />
            }
            onClick={() => setOpenDocumentStore(!openDocumentStore)}>
            {openDocumentStore ? 'Đóng' : 'Mở'} upload
          </Button>
          <Button
            icon={<i className="fa fa-print" />}
            type="primary"
            style={{ backgroundColor: '#155E75' }}
            onClick={() => {
              setViewFileOnly(false)
              setOpenDocumentStore(false)
              setFormMode(FORM_MODE.view)

              handlePrintPDF(`PhieuDieuTri_${medicalRecordDetail?.title || ''}`)
            }}>
            In phiếu
          </Button>
        </div>

        <div
          className="mt-2 mb-4 shadow-md p-3 rounded-sm"
          style={{ display: openDocumentStore ? 'block' : 'none' }}>
          <DocumentStore
            dataSource={lists.medical_record_form.listName}
            parentID={4} // 4 is DocumentStore
            storeID={selectedMedicalRecordId}
            mode={'Edit'}
            setAttachments={setAttachments}
          />
        </div>

        <div id="medical-record-form-print" hidden={viewFileOnly} className="mt-3">
          <Form form={form} scrollToFirstError>
            <h4 className="text-center fw-bold">PHIẾU THEO DÕI ĐIỀU TRỊ</h4>

            {/* top content */}
            <div className="row px-3">
              {/* left */}
              <div className="col-7">
                <div>Sở Y TẾ TP. Hồ Chí Minh</div>
                <div>Bệnh viện FV</div>
                <div>Họ tên người bệnh: {mainVisit?.fullname || ''}</div>
                <div className="d-flex gap-2 align-items-center">
                  <div className="me-2">Khoa:</div>
                  <div style={{ minWidth: '200px', width: '100%' }}>
                    <MedicalRecordField
                      form={form}
                      required={true}
                      formMode={formMode}
                      fieldName="departmentId"
                      fieldType={MEDICAL_RECORD_FIELD_TYPE.SELECT}
                      selectOptions={departmentMappings.map((dept) => ({
                        value: dept.department_id,
                        label: dept.ten_khoa_vn || dept.ten_khoa_en,
                      }))}
                    />
                  </div>
                </div>
                <div className="d-flex gap-2 align-items-center">
                  <div>Buồng:</div>
                  <MedicalRecordField
                    form={form}
                    formMode={formMode}
                    fieldName="room"
                    fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXT}
                    underlineLabel={true}
                  />
                </div>
                {/* primary flag of api_patient_visit_medical_coding_view */}
                <div>
                  Chẩn đoán:{' '}
                  <MedicalRecordField
                    form={form}
                    formMode={formMode}
                    fieldName="diagnosis"
                    fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXT}
                  />
                </div>
              </div>

              {/* right */}
              <div className="col-5">
                <div>Số vào viện: {mainVisit?.visible_patient_id || ''}</div>
                <div>
                  Tuổi: {mainVisit?.dob ? dayjs().diff(mainVisit.dob, 'year') : ''}{' '}
                  <span className="ps-4" /> Giới tính: {mainVisit?.sex === 'Male' ? 'Nam' : 'Nữ'}
                </div>
                <div className="d-flex gap-2 align-items-center">
                  Giường:{' '}
                  <MedicalRecordField
                    form={form}
                    formMode={formMode}
                    fieldName="bed"
                    fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXT}
                    underlineLabel={true}
                  />
                </div>
              </div>
            </div>

            {/* main content */}
            <div className="container-fluid mt-2">
              {/* table */}
              <table style={{ width: '100%', borderCollapse: 'collapse' }} border={1}>
                <thead>
                  <tr>
                    <th className="fw-bold text-md text-center" style={borderTable}>
                      NGÀY GIỜ
                    </th>
                    <th className="fw-bold text-md text-center" style={borderTable}>
                      DIỄN TIẾN BỆNH
                    </th>
                    <th className="fw-bold text-md text-center" style={borderTable}>
                      Y LỆNH
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td style={{ minWidth: 100, width: 100, ...borderTable }}>
                      {medicalRecordDetail?.title
                        ? dayjs(medicalRecordDetail?.title).format('DD/MM/YYYY HH:mm')
                        : ''}
                    </td>

                    <td style={borderTable} width={699}>
                      <MedicalRecordField
                        form={form}
                        formMode={formMode}
                        fieldName="visitReason"
                        label="Lý do khám bệnh:"
                        templateOptions={visitReasonOptions}
                        underlineLabel={true}
                      />
                      <div className="mt-2"></div>

                      <MedicalRecordField
                        form={form}
                        formMode={formMode}
                        fieldName="treatmentResponse"
                        label="Đáp ứng điều trị:"
                        templateOptions={treatmentResponseOptions}
                        underlineLabel={true}
                      />
                      <div className="mt-2"></div>

                      <MedicalRecordField
                        form={form}
                        formMode={formMode}
                        fieldName="testResult"
                        label="Kết quả xét nghiệm:"
                        templateOptions={testResultOptions}
                        underlineLabel={true}
                      />
                      <div className="mt-2"></div>

                      <MedicalRecordField
                        form={form}
                        formMode={formMode}
                        fieldName="cdhaResult"
                        label="Kết quả CĐHA:"
                        templateOptions={cdhaResultOptions}
                        underlineLabel={true}
                      />
                      <div className="mt-2"></div>

                      <MedicalRecordField
                        form={form}
                        formMode={formMode}
                        fieldName="disease_progression"
                        label="Giai đoạn bệnh:"
                        underlineLabel={true}
                      />

                      <MedicalRecordField
                        form={form}
                        formMode={formMode}
                        fieldName="conclusion"
                        label="Kết luận:"
                        templateOptions={conclusionOptions}
                        underlineLabel={true}
                        additionalButtons={
                          <Button
                            className="ms-2"
                            icon={<i className="fa-regular fa-square-plus"></i>}
                            size="small"
                            onClick={() => {
                              if (!chargeConclusion) {
                                app.message.warning('Chưa có kết luận nào được charge')
                                return
                              }

                              form.setFieldsValue({
                                conclusion:
                                  form.getFieldValue('conclusion') || '' + '\n' + chargeConclusion,
                              })
                            }}>
                            Chèn kết luận từ charge
                          </Button>
                        }
                      />

                      <div className="mt-2"></div>
                      <MedicalRecordField
                        form={form}
                        formMode={formMode}
                        fieldName="treatment"
                        label="Điều trị:"
                        templateOptions={treatmentOptions}
                        underlineLabel={true}
                      />
                    </td>

                    <td style={borderTable} width={699}>
                      <MedicalRecordField form={form} formMode={formMode} fieldName="note" />
                      <MedicalRecordField
                        form={form}
                        formMode={formMode}
                        fieldName="testDescription"
                        label="Xét nghiệm:"
                        underlineLabel={true}
                        additionalButtons={
                          <Button
                            className="ms-2"
                            icon={<i className="fa-regular fa-square-plus"></i>}
                            size="small"
                            onClick={() => {
                              if (!chargeTestDescription) {
                                app.message.warning('Chưa có xét nghiệm nào được charge')
                                return
                              }
                              const currentText = form.getFieldValue('testDescription')
                              form.setFieldsValue({
                                testDescription:
                                  currentText + (currentText ? '\n' : '') + chargeTestDescription,
                              })
                            }}>
                            Chèn xét nghiệm từ charge
                          </Button>
                        }
                      />
                      <div className="mt-2"></div>

                      <MedicalRecordField
                        form={form}
                        formMode={formMode}
                        fieldName="cdhaDescription"
                        label="CĐHA:"
                        underlineLabel={true}
                        additionalButtons={
                          <Button
                            className="ms-2"
                            icon={<i className="fa-regular fa-square-plus"></i>}
                            size="small"
                            onClick={() => {
                              if (!chargeCdhaDescription) {
                                app.message.warning('Chưa có CĐHA nào được charge')
                                return
                              }

                              form.setFieldsValue({
                                cdhaDescription:
                                  form.getFieldValue('cdhaDescription') +
                                  '\n' +
                                  chargeCdhaDescription,
                              })
                            }}>
                            Chèn CĐHA từ charge
                          </Button>
                        }
                      />
                      <div className="mt-2"></div>

                      <MedicalRecordField
                        form={form}
                        formMode={formMode}
                        fieldName="chemotherapyCycle"
                        customField={<Input className="fw-bold" placeholder="Vui lòng nhập" />}
                        renderValue={(fieldValue) => <b>{fieldValue}</b>}
                        underlineLabel={true}
                      />
                      <div className="mt-2"></div>

                      <MedicalRecordField
                        form={form}
                        formMode={formMode}
                        fieldName="medicineDescription"
                        underlineLabel={true}
                        additionalButtons={
                          <Button
                            loading={isChemoPrescriptionFetching}
                            icon={<i className="fa-solid fa-arrow-rotate-right"></i>}
                            size="small"
                            onClick={() => {
                              setResetMedicineDescription(true)
                              refetchChemoPrescription()
                            }}>
                            Lấy lại dữ liệu từ toa thuốc
                          </Button>
                        }
                      />
                      <div className="mt-2"></div>

                      <MedicalRecordField
                        form={form}
                        formMode={formMode}
                        fieldName="prescription"
                        label="ĐƠN THUỐC VỀ:"
                        underlineLabel={true}
                        additionalButtons={
                          <Button
                            loading={isGeneralPrescriptionFetching}
                            className="ms-2"
                            icon={<i className="fa-solid fa-arrow-rotate-right"></i>}
                            size="small"
                            onClick={() => {
                              setResetPrescription(true)
                              refetchGeneralPrescription()
                            }}>
                            Lấy lại dữ liệu từ toa thuốc
                          </Button>
                        }
                      />
                      <div className="mt-2"></div>

                      <MedicalRecordField
                        form={form}
                        formMode={formMode}
                        fieldName="nextVisit"
                        label="Hẹn lần khám tới:"
                        underlineLabel={true}
                      />
                    </td>
                  </tr>
                </tbody>

                <tfoot>
                  <tr>
                    <td style={{ borderTop: '1px solid gray' }} colSpan={3}></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </Form>
        </div>

        <div hidden={!viewFileOnly} className="mt-3">
          <PdfViewer
            serverRelativeUrl={attachments[0]?.ServerRelativeUrl}
            fileName={attachments[0]?.Name}
          />
        </div>
      </div>

      {openChangeEmployee && (
        <Modal
          title="Chọn bác sĩ chính"
          open={openChangeEmployee}
          footer={false}
          onCancel={() => setOpenChangeEmployee(false)}>
          <div>
            <LazySelect
              className="w-full"
              list={lists.employee_dataset}
              keyProp="employee_id"
              searchFields={['employee_name', 'user_name']}
              placeholder="Chọn bác sĩ chính"
              filter="employee_id ne null"
              defaultSelected={selectedEmployee}
              renderOption={(item) => (
                <Select.Option key={item.employee_id} value={item.employee_id} item={item}>
                  {item.employee_name}
                </Select.Option>
              )}
              setSelectedUser={(item) => {
                setSelectedEmployee(item)
              }}
            />
            <div className="d-flex justify-content-end gap-2 mt-3">
              <AsyncButton
                icon={<i className="fa fa-save" />}
                type="primary"
                onClick={async () => {
                  await handleSaveMedicalRecordForm()
                  setOpenChangeEmployee(false)
                }}>
                Lưu
              </AsyncButton>
            </div>
          </div>
        </Modal>
      )}
    </>
  )
}

export default TreatmentForm
