import { But<PERSON>, Mo<PERSON>, <PERSON> } from 'antd'
import PropTypes from '../../common/PropTypes'
import { usePatientVisitHistory } from './hooks/usePatientVisitHistory'
import {
  ACTION_VISIT_HISTORY_TITLE,
  VISIT_SUBSCRIBE_POLICY_HISTORY_SNAPSHOT,
} from './VisitConstant'
import { displayDate, displayDateTime, makeFilterColumns } from '../../common/helpers'
import { VISIT_CHARGE_HISTORY_SNAPSHOT } from './ChargeDetail/ChargeDetailConstant'
import { usePatientVisit } from './hooks/usePatientVisit'

const propTypes = {
  isVisibleVisitTrackingHistoryPopup: PropTypes.bool.isRequired,
  setIsVisibleVisitTrackingHistoryPopup: PropTypes.func.isRequired,
  currentPatientVisit: PropTypes.object.isRequired,
}

function VisitTrackingHistoryPopup({
  isVisibleVisitTrackingHistoryPopup,
  setIsVisibleVisitTrackingHistoryPopup,
  currentPatientVisit,
}) {
  const { visitHistoryData, users } = usePatientVisitHistory(currentPatientVisit?.patient_visit_id)
  const { combinedPatientVisitMappingViews } = usePatientVisit(
    currentPatientVisit?.patient_visit_id,
  )

  const columns = [
    {
      title: 'Người thực hiện',
      dataIndex: 'performed_by',
      key: 'performed_by',
      render: (text) => {
        const user = users?.find((user) => user.user_id === text)
        return user ? `${user?.employee_number} - ${user?.employee_name}` : 'N/A'
      },
    },
    {
      title: 'Hành động',
      dataIndex: 'action',
      key: 'action',
      render: (text) => ACTION_VISIT_HISTORY_TITLE?.[text]?.nameL || 'N/A',
    },
    {
      title: 'Lượt khám thao tác',
      dataIndex: 'patient_visit_id',
      key: 'patient_visit_id',
      render: (_, r) => {
        const visit = combinedPatientVisitMappingViews?.find(
          (item) => item.patient_visit_id === (r.merged_patient_visit_id || r.patient_visit_id),
        )

        return visit?.visit_code + ' - ' + displayDate(visit?.actual_visit_datetime)
      },
    },
    {
      title: 'Thời gian',
      align: 'right',
      dataIndex: 'performed_date_time',
      key: 'performed_date_time',
      render: (text) => displayDateTime(text),
    },
    {
      title: 'Ghi chú',
      dataIndex: 'note',
      key: 'note',
      render: (text, r) => (
        <div>
          <div>
            <pre>{text}</pre>
          </div>
          <div>
            {VISIT_CHARGE_HISTORY_SNAPSHOT[r.action]?.getSnapshot(r.data_snapshot)?.displayL ||
              VISIT_SUBSCRIBE_POLICY_HISTORY_SNAPSHOT[r.action]?.getSnapshot(r.data_snapshot)
                ?.displayL}
          </div>
        </div>
      ),
    },
  ]

  return (
    <Modal
      className="custom-modal"
      width={2000}
      title="Lịch sử thao tác"
      open={isVisibleVisitTrackingHistoryPopup}
      onClose={() => {
        setIsVisibleVisitTrackingHistoryPopup(false)
      }}
      onCancel={() => {
        setIsVisibleVisitTrackingHistoryPopup(false)
      }}
      footer={[
        <Button key="cancel" onClick={() => setIsVisibleVisitTrackingHistoryPopup(false)}>
          Hủy
        </Button>,
      ]}>
      <div className="flex flex-col gap-3">
        <Table
          size="small"
          dataSource={visitHistoryData}
          columns={columns}
          rowKey="patient_visit_history_id"
          pagination={false}
        />
      </div>
    </Modal>
  )
}

VisitTrackingHistoryPopup.propTypes = propTypes
export default VisitTrackingHistoryPopup
