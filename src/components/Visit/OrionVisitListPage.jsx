import { Button, Form, Input, Popconfirm, Table, Modal, Tag, Popover } from 'antd'
import { useOrionPatientVisit } from './hooks/useOrionPatientVisit'
import { displayDate, displayDateTime, handleError } from '../../common/helpers'
import PropTypes from '../../common/PropTypes'
import dayjs from '../../common/dayjs'
import { FORMAT_DATE } from '../../common/constant'
import { ACTION_VISIT_HISTORY, PROCESSING_STATUS } from './VisitConstant'
import { useEffect, useState } from 'react'
import { useForm } from 'antd/es/form/Form'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'
import SearchAndAddVisitPopup from './SearchAndAddVisitPopup'
import {
  addMultiMergedPatientVisits,
  getHealthInsuranceCards,
  getHealthInsuranceCardsByPatientVisitId,
  getPatientDatasetByFilterEndWithHN,
} from './VisitService'
import _ from 'lodash'
import { usePatientVisitHistory } from './hooks/usePatientVisitHistory'
import { deleteListItemService, getItemService, getItemsService } from '../../common/services'
import lists from '../../common/lists'
import AsyncButton from '../../common/components/AsyncButton'
import useApp from 'antd/es/app/useApp'
import { useUI } from '../../common/UIProvider'

const propsType = {
  filterVisitDate: PropTypes.array,
  filterHN: PropTypes.string,
}

function OrionVisitListPage({
  filterVisitDate,
  filterHN,
  ss_confirmation_flag,
  processing_status,
  selectedVisitTypes,
}) {
  const formatedDate = filterVisitDate?.map((date) => {
    if (date) return date.format(FORMAT_DATE)
    return null
  })

  const { addPatientVisitHistory } = usePatientVisitHistory()
  const app = useApp()
  const ui = useUI()
  const [formModal] = useForm()
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [selectedPatientVisit, setSelectedPatientVisit] = useState(null)
  const [initialCurrentPatient, setInitialCurrentPatient] = useState(null)
  const {
    orionPatientVisitQuery,
    orionPatientVisits,
    updatePatientVisitMutation,
    createPatientVisitHistory,
    refetchOrionPatientVisit,
  } = useOrionPatientVisit(
    formatedDate,
    filterHN,
    ss_confirmation_flag,
    processing_status,
    selectedVisitTypes,
  )

  // Hàm hiển thị modal nhập lý do
  const showNoteModal = ({
    title,
    onConfirm,
    placeholder = 'Nhập lý do...',
    defaultValue = '',
    isNoteRequired = false,
  }) => {
    formModal.setFieldsValue({ note: defaultValue })
    app.modal.confirm({
      title,
      content: (
        <Form form={formModal} layout="vertical" style={{ width: '100%' }}>
          <Form.Item
            name="note"
            label="Lý do"
            initialValue={defaultValue}
            rules={[
              {
                required: isNoteRequired,
                message: 'Lý do là bắt buộc!',
              },
            ]}>
            <Input.TextArea autoSize={{ minRows: 2 }} placeholder={placeholder} rows={3} />
          </Form.Item>
        </Form>
      ),
      onOk: async () => {
        try {
          await formModal.validateFields()
          const noteValue = formModal.getFieldValue('note')

          await onConfirm(noteValue)
        } catch (error) {
          handleError(error)
        }
      },
      okText: 'Xác nhận',
      cancelText: 'Hủy',
      maskClosable: true,
    })
  }

  const handleOpenModal = () => {
    setIsModalVisible(true)
  }

  const handleCloseModal = () => {
    setInitialCurrentPatient(null)
    setIsModalVisible(false)
  }

  // return false if notfound treatment_course, else return true and merge visit
  const handleCheckAndMergeVisit = async (patientVisit, note) => {
    const patientVisitIdToMerge = patientVisit?.patient_visit_id
    const patientId = patientVisit?.patient_id

    if (!patientVisitIdToMerge || !patientId) {
      return false
    }

    try {
      let parentPatientVisitId = await getItemsService(lists.patient_visit_mapping_view, {
        filter: `patient_id eq ${patientId} 
          and parent_patient_visit_id eq null 
          and treatment_course_flag eq true 
          and treatment_course_start_date le ${dayjs(patientVisit?.actual_visit_datetime).toISOString()} 
          and treatment_course_end_date ge ${dayjs(patientVisit?.actual_visit_datetime).toISOString()}`,
        top: 1,
        count: false,
      })
      const parentPatientVisit = parentPatientVisitId.value[0]
      parentPatientVisitId = parentPatientVisitId.value[0]?.patient_visit_id

      if (!parentPatientVisitId) {
        return false
      }

      await addMultiMergedPatientVisits(
        [patientVisitIdToMerge],
        parentPatientVisitId,
        currentUser,
        patientId,
      )

      // change parentPatientVisitId processing_status
      await updatePatientVisitMutation.mutateAsync({
        id: parentPatientVisitId,
        data: {
          processing_status: PROCESSING_STATUS.WAITING_BHYT.name_e,
        },
      })

      await updatePatientVisitMutation.mutateAsync({
        id: patientVisit?.patient_visit_id,
        data: {
          ss_confirmation_flag: true,
          ss_confirmation_note: note || null,
        },
      })

      addPatientVisitHistory.mutateAsync({
        historyData: {
          patient_visit_id: parentPatientVisitId,
          merged_patient_visit_id: patientVisitIdToMerge,
          note: 'Auto merge visit during insurance confirmation',
        },
        action: ACTION_VISIT_HISTORY.MERGE_VISIT,
      })

      addPatientVisitHistory.mutateAsync({
        historyData: {
          patient_visit_id: parentPatientVisitId,
          merged_patient_visit_id: patientVisitIdToMerge,
          note: 'Auto change status when new visit is merged',
        },
        action: ACTION_VISIT_HISTORY.SAVE_INFO,
      })

      ui.notiSuccess(
        'Tự động merge lượt khám',
        `Lượt khám đã tự động merge vào đợt điều trị bắt đầu từ ${displayDate(parentPatientVisit?.actual_visit_datetime)}. 
        Lượt khám gốc: ${parentPatientVisit?.visit_code}`,
      )

      return true
    } catch (error) {
      handleError(error, 'handleMergeVisit')
    }
  }

  const checkExistPatientVisitId = async (patientVisitId) => {
    try {
      const result = await getItemService(lists.api_orion_patient_visit_view, patientVisitId)

      if (result?.ss_confirmation_flag !== null) {
        return result
      }
    } catch (error) {
      // ignore error
    }

    return null
  }

  const handleConfirmInsurance = (record) => {
    showNoteModal({
      title: 'Xác nhận tham gia BHYT',
      onConfirm: async (note) => {
        const isAutoMerge = await handleCheckAndMergeVisit(record, note)

        if (isAutoMerge) {
          refetchOrionPatientVisit()
          return
        }

        const existPatientVisit = await checkExistPatientVisitId(record.patient_visit_id)

        if (!existPatientVisit) {
          await createPatientVisitHistory.mutateAsync({
            data: {
              card_type_id: record.card_type_id,
              designated_illness_reason: record.designated_illness_reason,
              emergency_case_flag: record.emergency_case_flag,
              ethnicity_id: record.ethnicity_id,
              free_copay_flag: record.free_copay_flag,
              medical_record_code: record.medical_record_code,
              note: record.note,
              occupation_id: record.occupation_id,
              referral_code: record.referral_code,
              referral_date: record.referral_date,
              referral_disposition_rcd: record.referral_disposition_rcd,
              referral_reason: record.referral_reason,
              referral_type_rcd: record.referral_type_rcd,
              free_copay_start_date: record.free_copay_start_date,
              free_copay_end_date: record.free_copay_end_date,
              right_channel_flag: record.right_channel_flag,
              serious_illness_flag: record.serious_illness_flag,
              serious_illness_icd10id: record.serious_illness_icd10id,
              serious_illness_icd_code: record.serious_illness_icd_code,
              ss_confirmation_flag: true,
              ss_confirmation_note: note || null,
              patient_visit_id: record?.patient_visit_id,
              patient_id: record?.patient_id,
              processing_status: PROCESSING_STATUS.WAITING_FULLFILL_INFO.name_e,
              lu_updated: dayjs(),
              lu_user_id: currentUser?.User_id,
            },
          })

          await addPatientVisitHistory.mutateAsync({
            historyData: {
              patient_visit_id: record?.patient_visit_id,
            },
            action: ACTION_VISIT_HISTORY.CREATE_VISIT,
          })
        } else {
          await updatePatientVisitMutation.mutateAsync({
            id: record.patient_visit_id,
            data: {
              ss_confirmation_flag: true,
              ss_confirmation_note: note || null,
              processing_status: PROCESSING_STATUS.WAITING_FULLFILL_INFO.name_e,
            },
          })
        }
      },
      isNoteRequired: false,
    })
  }

  const handleConfirmAndCreateVisit = (record) => {
    showNoteModal({
      title: 'Xác nhận và tạo lượt khám',
      onConfirm: async (note) => {
        const isAutoMerge = await handleCheckAndMergeVisit(record, note)

        if (isAutoMerge) {
          refetchOrionPatientVisit()
          return
        }

        const existPatientVisit = await checkExistPatientVisitId(record.patient_visit_id)

        if (!existPatientVisit) {
          await createPatientVisitHistory.mutateAsync({
            data: {
              card_type_id: record.card_type_id,
              designated_illness_reason: record.designated_illness_reason,
              emergency_case_flag: record.emergency_case_flag,
              ethnicity_id: record.ethnicity_id,
              free_copay_flag: record.free_copay_flag,
              medical_record_code: record.medical_record_code,
              note: record.note,
              occupation_id: record.occupation_id,
              referral_code: record.referral_code,
              referral_date: record.referral_date,
              referral_disposition_rcd: record.referral_disposition_rcd,
              referral_reason: record.referral_reason,
              referral_type_rcd: record.referral_type_rcd,
              free_copay_start_date: record.free_copay_start_date,
              free_copay_end_date: record.free_copay_end_date,
              right_channel_flag: record.right_channel_flag,
              serious_illness_flag: record.serious_illness_flag,
              serious_illness_icd10id: record.serious_illness_icd10id,
              serious_illness_icd_code: record.serious_illness_icd_code,
              ss_confirmation_flag: true,
              ss_confirmation_note: note || null,
              patient_visit_id: record?.patient_visit_id,
              patient_id: record?.patient_id,
              processing_status: PROCESSING_STATUS.WAITING_FULLFILL_INFO.name_e,
              lu_updated: dayjs(),
              lu_user_id: currentUser?.User_id,
            },
          })

          await addPatientVisitHistory.mutateAsync({
            historyData: {
              patient_visit_id: record?.patient_visit_id,
            },
            action: ACTION_VISIT_HISTORY.CREATE_VISIT,
          })

          setSelectedPatientVisit(record)

          const patients = await getPatientDatasetByFilterEndWithHN(record.visible_patient_id)
          setInitialCurrentPatient(patients.value[0])
        } else {
          await updatePatientVisitMutation.mutateAsync({
            id: record.patient_visit_id,
            data: {
              ss_confirmation_flag: true,
              ss_confirmation_note: note || null,
              processing_status: PROCESSING_STATUS.WAITING_FULLFILL_INFO.name_e,
            },
          })
        }
      },
      isNoteRequired: false,
    })
  }

  const handleCreateVisit = async (record) => {
    setSelectedPatientVisit(record)
    const patients = await getPatientDatasetByFilterEndWithHN(record.visible_patient_id)
    setInitialCurrentPatient(patients.value[0])
  }

  const handleRejectInsurance = (record) => {
    showNoteModal({
      title: 'Xác nhận không tham gia BHYT',
      onConfirm: async (note) => {
        await createPatientVisitHistory.mutateAsync({
          data: {
            card_type_id: record.card_type_id,
            designated_illness_reason: record.designated_illness_reason,
            emergency_case_flag: record.emergency_case_flag,
            ethnicity_id: record.ethnicity_id,
            free_copay_flag: record.free_copay_flag,
            medical_record_code: record.medical_record_code,
            note: record.note,
            occupation_id: record.occupation_id,
            referral_code: record.referral_code,
            referral_date: record.referral_date,
            referral_disposition_rcd: record.referral_disposition_rcd,
            referral_reason: record.referral_reason,
            referral_type_rcd: record.referral_type_rcd,
            free_copay_start_date: record.free_copay_start_date,
            free_copay_end_date: record.free_copay_end_date,
            right_channel_flag: record.right_channel_flag,
            serious_illness_flag: record.serious_illness_flag,
            serious_illness_icd10id: record.serious_illness_icd10id,
            serious_illness_icd_code: record.serious_illness_icd_code,
            patient_visit_id: record?.patient_visit_id,
            patient_id: record?.patient_id,
            ss_confirmation_flag: false,
            ss_confirmation_note: note || null,
            lu_updated: dayjs(),
            lu_user_id: currentUser?.User_id,
          },
        })
      },
      isNoteRequired: true,
    })
  }

  const columns = [
    {
      title: 'Loại',
      dataIndex: 'processing_status',
      key: 'processing_status',
      width: 120,
      onCell: (record) => ({ colSpan: record.children ? 2 : 1 }),
      render: (status, record) => (record.isParent ? <b>{status}</b> : record.visit_type_name_e),
    },
    {
      title: 'HN',
      dataIndex: 'visible_patient_id',
      key: 'visible_patient_id',
      width: 100,
      onCell: (record) => ({ colSpan: record.children ? 0 : 1 }),
      render: (text, record) => (record.children ? '' : text),
    },
    {
      title: 'Tên bệnh nhân',
      dataIndex: 'fullname',
      width: 150,
      key: 'fullName',
    },
    // {
    //   title: 'Ngày sinh',
    //   dataIndex: 'dob',
    //   key: 'dob',
    //   align: 'right',
    //   render: (date, record) => (record.children ? '' : displayDate(date))
    // },
    // {
    //   title: 'Giới tính',
    //   dataIndex: 'sex',
    //   key: 'gender'
    // },
    {
      title: 'Mã lượt khám',
      dataIndex: 'visit_code',
      width: 110,
      key: 'visitCode',
    },
    {
      title: 'Ngày bắt đầu lượt khám',
      dataIndex: 'actual_visit_datetime',
      key: 'startDate',
      width: 140,
      align: 'right',
      render: (date, record) => (record.children ? '' : displayDateTime(date)),
    },
    {
      title: 'Ngày kết thúc lượt khám',
      dataIndex: 'closure_visit_datetime',
      key: 'endDate',
      width: 140,
      align: 'right',
      render: (date, record) => (record.children ? '' : displayDateTime(date)),
    },
    {
      title: 'Trạng thái xác nhận',
      dataIndex: 'ss_confirmation_flag',
      key: 'ss_confirmation_flag',
      width: 140,
      render: (item, record) => {
        if (item === null) return <Tag color="blue">Chưa xác nhận</Tag>

        if (item === false) {
          return (
            <div>
              <Tag color="red">
                Không tham gia BHYT
                {record.ss_confirmation_note && (
                  <div style={{ marginTop: 4 }}>
                    <span className="text-red-700">Lý do: {record.ss_confirmation_note}</span>
                  </div>
                )}
              </Tag>
            </div>
          )
        }

        return <Tag color="green">Có tham gia BHYT</Tag>
      },
    },
    // {
    //   title: 'Theo đợt điều trị',
    //   dataIndex: 'treatment_course_flag',
    //   key: 'treatment_course_flag',
    //   align: 'center',
    //   width: 140,
    //   render: (text) =>
    //     text ? <FontAwesomeIcon icon={faCheckSquare} color={COLOR.blueLight} /> : null
    // },
    // {
    //   title: 'Ngày kết thúc điều trị',
    //   dataIndex: 'treatment_course_end_date',
    //   key: 'treatment_course_end_date',
    //   align: 'right',
    //   width: 140,
    //   render: (date, record) => (record.children ? '' : displayDateTime(date))
    // },
    {
      title: 'Hành động',
      key: 'action',
      fixed: 'right',
      width: 110,
      render: (_, record) =>
        record.children ? null : (
          <div className="d-flex gap-1 flex-column py-1">
            {record.processing_status === PROCESSING_STATUS.WAITING_FULLFILL_INFO.name_e &&
            record.ss_confirmation_flag === true ? (
              <>
                <AsyncButton
                  type="primary"
                  size="small"
                  className="btn-wrap"
                  ghost
                  onClick={async () => await handleCreateVisit(record)}>
                  Nhập thông tin
                </AsyncButton>
              </>
            ) : (
              record.ss_confirmation_flag === null && (
                <Popconfirm
                  title="Xác nhận BHYT"
                  description={
                    <div style={{ display: 'flex', gap: 8 }}>
                      <AsyncButton
                        ghost
                        type="primary"
                        size="small"
                        onClick={async () => await handleConfirmInsurance(record)}>
                        Xác nhận BHYT
                      </AsyncButton>
                      <AsyncButton
                        size="small"
                        type="primary"
                        onClick={async () => await handleConfirmAndCreateVisit(record)}>
                        Xác nhận và Tạo lượt khám
                      </AsyncButton>
                    </div>
                  }
                  okButtonProps={{ style: { display: 'none' } }}
                  cancelButtonProps={{ style: { display: 'none' } }}>
                  <Popover placement="left" content="Có tham gia BHYT">
                    <Button type="primary" icon={<i className="fa fa-check" />} size="small">
                      BHYT
                    </Button>
                  </Popover>
                </Popconfirm>
              )
            )}

            {record.ss_confirmation_flag === null && (
              <Popconfirm
                title="Bạn chắc chắn không tham gia BHYT?"
                description={
                  <div style={{ display: 'flex', gap: 8 }}>
                    <Button size="small" ghost danger onClick={() => handleRejectInsurance(record)}>
                      Xác nhận không tham gia
                    </Button>
                  </div>
                }
                okButtonProps={{ style: { display: 'none' } }}
                cancelButtonProps={{ style: { display: 'none' } }}>
                <Popover placement="left" content="Không tham gia BHYT">
                  <Button type="default" danger icon={<i className="fa fa-times" />} size="small">
                    BHYT
                  </Button>
                </Popover>
              </Popconfirm>
            )}

            {record.ss_confirmation_flag !== null && (
              <Popconfirm
                title="Xác nhận lại"
                onConfirm={async () => {
                  // get cards of visit and delete all cards
                  const cards = await getHealthInsuranceCardsByPatientVisitId(
                    record.patient_visit_id,
                    '',
                    1000,
                  )
                  await Promise.all(
                    cards.value.map((card) =>
                      deleteListItemService(
                        lists.health_insurance_card,
                        card.health_insurance_card_id,
                      ),
                    ),
                  )

                  await deleteListItemService(lists.patient_visit, record.patient_visit_id)
                  refetchOrionPatientVisit()
                }}>
                <AsyncButton danger size="small" icon={<i className="fa fa-trash ms-1" />}>
                  Xác nhận lại
                </AsyncButton>
              </Popconfirm>
            )}
          </div>
        ),
    },
  ]

  useEffect(() => {
    if (initialCurrentPatient) {
      handleOpenModal()
    }
  }, [initialCurrentPatient])

  return (
    <div>
      <div className="row mt-2"></div>
      <Table
        dataSource={orionPatientVisits}
        className="custom-table"
        size="small"
        scroll={{ x: columns.map((c) => c.width).reduce((a, b) => a + b) + 100 }}
        columns={columns}
        rowKey="patient_visit_id"
        loading={orionPatientVisitQuery.isLoading || orionPatientVisitQuery.isFetching}
        pagination={{
          defaultPageSize: 10,
        }}
      />

      <Modal
        className="custom-modal"
        title="TẠO LƯỢT KHÁM DÙNG BHYT"
        width={2000}
        open={isModalVisible}
        onCancel={handleCloseModal}
        destroyOnClose
        maskClosable={false}
        keyboard={false}
        footer={null}>
        <SearchAndAddVisitPopup
          onSave={() => {
            setIsModalVisible(false)
            setInitialCurrentPatient(null)
            refetchOrionPatientVisit()
          }}
          initialSelectedPatientVisitId={selectedPatientVisit?.patient_visit_id || null}
          initialCurrentPatient={initialCurrentPatient}
          onBack={handleCloseModal}
          formMode="edit"
        />
      </Modal>
    </div>
  )
}

OrionVisitListPage.propTypes = propsType

export default OrionVisitListPage
