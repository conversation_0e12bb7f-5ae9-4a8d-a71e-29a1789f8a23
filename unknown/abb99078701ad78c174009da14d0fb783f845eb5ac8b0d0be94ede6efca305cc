import React, { useEffect, useState } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'

import 'react-pdf/dist/Page/AnnotationLayer.css'
import 'react-pdf/dist/Page/TextLayer.css'
import { downloadBase64File, fileToBase64, handleError } from '../helpers'
import { getFileService } from '../services'
import { useUI } from '../UIProvider'
import { Button, Select } from 'antd'
import { preventZoomKey, preventZoomWheel } from '../../SI/helper'

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.mjs',
  import.meta.url,
).toString()

const PdfViewer = ({ serverRelativeUrl, fileName, fileContent, width = '100%' }) => {
  const ui = useUI()
  const [selectedFile, setSelectedFile] = useState()
  const [pageNumbers, setPageNumbers] = useState([])
  const [scale, setScale] = useState(1.5) // set default scale here: 1.25; 1.5
  const [isAllPageLoaded, setIsAllPageLoaded] = useState(false)

  const getFileContent = async () => {
    try {
      let fileContent = await getFileService(serverRelativeUrl)

      setSelectedFile(await fileToBase64(fileContent))
    } catch (error) {
      handleError(error, 'getFileContent')
    }
  }

  // case serverRelativeUrl
  useEffect(() => {
    if (!serverRelativeUrl) {
      return
    }

    getFileContent()
  }, [serverRelativeUrl])

  // case fileContent
  useEffect(() => {
    if (!fileContent) {
      return
    }

    const initFileContent = async () => {
      setSelectedFile(await fileToBase64(fileContent))
    }

    initFileContent()
  }, [fileContent])

  // prevent zoom event
  useEffect(() => {
    document.body.onwheel = preventZoomWheel
    document.body.onkeydown = preventZoomKey
  }, [])

  function onDocumentLoadSuccess(args) {
    const arr = []
    for (let i = 1; i <= args.numPages; i++) {
      arr.push(i)
    }

    setPageNumbers(arr)
  }

  const onPageRenderSuccess = (page) => {
    if (page.pageNumber === pageNumbers.length) {
      setIsAllPageLoaded(true)
    }
  }

  return (
    <div className="pdf-viewer">
      <div className="pdf-viewer--header">
        <div className="pdf-viewer--header--right d-flex align-items-center">
          <Button
            onClick={(e) => {
              e.preventDefault()

              downloadBase64File(fileName, selectedFile)
            }}
            className="me-2"
            icon={<i className="fa-solid fa-arrow-down"></i>}>
            Tải về
          </Button>
          <Select
            className="me-2"
            style={{ width: 70 }}
            value={scale * 100}
            defaultValue={150}
            onChange={(value) => setScale(value / 100)}>
            {[50, 75, 100, 125, 150, 175, 200].map((n) => (
              <Select.Option key={n}>{n}</Select.Option>
            ))}
          </Select>
          <Button
            shape="circle"
            className="me-2"
            onClick={() => setScale(parseFloat(scale) - 0.25 < 0 ? 0 : parseFloat(scale) - 0.25)}
            icon={<i className="fa-solid fa-minus" style={{ marginLeft: '0.5em' }}></i>}></Button>

          <Button
            shape="circle"
            className="me-2"
            onClick={() => setScale(parseFloat(scale) + 0.25)}
            icon={<i className="fa-solid fa-plus" style={{ marginLeft: '0.5em' }}></i>}></Button>
          <Button ghost type="primary" onClick={() => setScale(1)}>
            Reset
          </Button>
        </div>
      </div>

      <div className="pdf-viewer--content">
        <Document
          className="pdf-viewer--document"
          style={{
            width: width,
          }}
          file={selectedFile}
          onLoadSuccess={onDocumentLoadSuccess}>
          {pageNumbers.map((number) => (
            <Page
              renderTextLayer={false}
              onRenderSuccess={onPageRenderSuccess}
              scale={1 * scale}
              className="pdf-viewer--page"
              key={number}
              pageNumber={number}
            />
          ))}
        </Document>
      </div>
    </div>
  )
}

export default PdfViewer
