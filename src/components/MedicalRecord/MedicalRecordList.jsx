import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal } from 'antd'
import React, { useCallback, useEffect, useState } from 'react'
import TreatmentForm from './TreatmentForm'
import ConsultationForm from './ConsultationForm'
import OPDMedicalRecord from './OPDMedicalRecord'
import AsyncButton from '../../common/components/AsyncButton'
import COLOR from '../../common/color'
import {
  handleInitTreatmentForm,
  handleInitConsultationForm,
  handleInitOPDMedicalRecord,
  deleteMedicalRecordForm,
  handleInitApprovalLetterForm,
  handleInitMedicalOrderForm,
} from './MedicalRecordService'
import { useVisitChargeDetails } from '../Visit/hooks/useVisitChargeDetails'
import { useMedicalRecord } from '../../queryHooks/useMedicalRecord'
import {
  MEDICAL_RECORD_FORM_TYPE,
  MEDICAL_RECORD_VIEW_MODE,
  getEditFieldsByFormType,
} from './MedicalRecordConstant'
import useDeepCompareEffect from 'use-deep-compare-effect'
import { isEqual } from 'lodash'
import useApp from 'antd/es/app/useApp'
import ApprovalLetterForm from './ApprovalLetterForm'
import { usePatient } from '../../queryHooks/usePatient'
import MedicalRecordForm from './MedicalRecordForm'
import CommonMedicalRecordForm from './CommonMedicalRecordForm'
import MedicalOrderForm from './MedicalOrderForm'

/**
 * Ref: 1. Requirement/
 *      Training Documents/
 *      Chị Hạnh gửi/
 *      Các phiếu của Hồ sơ bệnh án/
 *      Gui Anh Si/
 *      Thư viện thông tin tờ điều trị.docx
 *  */
const MedicalRecordList = ({
  selectedVisit,
  mainVisit,
  patientId,
  medicalRecordViewMode = MEDICAL_RECORD_VIEW_MODE.BY_VISIT,
}) => {
  const app = useApp()

  // states
  const [selectedMedicalRecordId, setSelectedMedicalRecordId] = useState()
  const [selectedFormType, setSelectedFormType] = useState()
  const [isShowMenu, setIsShowMenu] = useState(true)
  const [menu, setMenu] = useState(() =>
    Object.entries(MEDICAL_RECORD_FORM_TYPE).map(([key, item]) => ({
      label: item.nameE,
      key: key,
    })),
  )
  const [openMedicalRecordForm, setOpenMedicalRecordForm] = useState({
    open: false,
    formType: null,
  })

  // hooks
  const { data: visitChargeDetailsData } = useVisitChargeDetails(selectedVisit)
  const { patient } = usePatient({ patientId: patientId })
  const useMedicalRecordOptions =
    medicalRecordViewMode === MEDICAL_RECORD_VIEW_MODE.BY_PATIENT
      ? { patientId: patientId, medicalRecordViewMode }
      : {
          patientVisitId: selectedVisit?.patient_visit_id,
          patientId: mainVisit?.patient_id,
          medicalRecordViewMode,
        }
  const {
    medicalRecordList,
    medicalRecordListQuery,
    refetchAll: refetchMedicalRecord,
  } = useMedicalRecord(useMedicalRecordOptions)

  // Handle delete medical record form - memoize to prevent recreation on each render
  const handleDeleteMedicalRecordForm = async (medicalRecordFormId) => {
    try {
      await deleteMedicalRecordForm(medicalRecordFormId)
      app.message.success('Xóa phiếu thành công')
      // Reset selected form if it was deleted
      if (selectedMedicalRecordId === medicalRecordFormId) {
        setSelectedMedicalRecordId(undefined)
        setSelectedFormType(undefined)
      }
      // Refresh the list
      refetchMedicalRecord()
    } catch (error) {
      app.message.error('Xóa phiếu thất bại: ' + error.message)
    }
  }

  // Update menu only when necessary using deep comparison
  useDeepCompareEffect(() => {
    if (medicalRecordListQuery.isLoading) {
      return
    }

    const newMenu = Object.entries(MEDICAL_RECORD_FORM_TYPE).map(([key, item]) => {
      return {
        label: item.nameE,
        key: key,
        children: medicalRecordList
          .filter((r) => r.medical_record_form_type_rcd === key)
          .map((child) => ({
            key: child.medical_record_form_id,
            label: (
              <div className="d-flex justify-content-between align-items-center">
                <span>{child.title}</span>
                <Popconfirm
                  title="Bạn có chắc chắn muốn xóa phiếu này?"
                  okText="Xóa"
                  cancelText="Hủy"
                  onConfirm={(e) => {
                    e.stopPropagation()
                    handleDeleteMedicalRecordForm(child.medical_record_form_id)
                  }}
                  onCancel={(e) => e.stopPropagation()}>
                  <Button
                    size="small"
                    danger
                    type="text"
                    icon={<i className="fa fa-trash ms-1" />}
                    className="me-2"
                    onClick={(e) => e.stopPropagation()}
                  />
                </Popconfirm>
              </div>
            ),
          })),
      }
    })

    const oldMenu = menu

    if (!isEqual(oldMenu, newMenu)) {
      setMenu(newMenu)
    }
  }, [medicalRecordList, medicalRecordListQuery.isLoading])

  const initMedicalRecordList = async () => {
    if (
      !!selectedVisit?.patient_visit_id &&
      !!mainVisit?.patient_visit_id &&
      visitChargeDetailsData?.[0]
    ) {
      await handleInitTreatmentForm(visitChargeDetailsData, selectedVisit, mainVisit)
      await handleInitConsultationForm(visitChargeDetailsData, selectedVisit, mainVisit)
      await handleInitOPDMedicalRecord(visitChargeDetailsData, selectedVisit, mainVisit)
      await handleInitMedicalOrderForm(visitChargeDetailsData, mainVisit)
    }

    if (!!patient || !!mainVisit) {
      await handleInitApprovalLetterForm(mainVisit, patient)
    }

    refetchMedicalRecord()
  }

  useDeepCompareEffect(() => {
    initMedicalRecordList()
  }, [visitChargeDetailsData, selectedVisit, mainVisit, patient])

  return (
    <>
      <div className="d-flex gap-1">
        <div>
          <Button
            block
            type="default"
            icon={
              isShowMenu ? (
                <i className="fa-solid fa-bars ms-1"></i>
              ) : (
                <i className="fa-solid fa-arrow-right"></i>
              )
            }
            onClick={() => setIsShowMenu(!isShowMenu)}>
            {isShowMenu ? 'Ẩn Menu' : 'Hiện Menu'}
          </Button>
        </div>
        <AsyncButton
          icon={<i className="fa fa-check" />}
          type="primary"
          variant="filled"
          color="green"
          onClick={initMedicalRecordList}>
          KIỂM TRA VÀ KHỞI TẠO PHIẾU
        </AsyncButton>
        <Button
          icon={<i className="fa fa-plus" />}
          variant="filled"
          color="blue"
          onClick={() => setOpenMedicalRecordForm({ open: true })}>
          TẠO MỚI PHIẾU
        </Button>

        <AsyncButton onClick={refetchMedicalRecord} icon={<i className="fa fa-refresh" />}>
          REFRESH
        </AsyncButton>
      </div>

      <div className="border-t my-2" style={{ border: `1px solid ${COLOR.yellow}` }}></div>

      <div className="d-flex gap-2">
        <div
          hidden={medicalRecordListQuery.isLoading || !isShowMenu}
          className="max-h-[calc(100vh-148px)] overflow-y-auto overflow-x-hidden w-[344px] sticky-top"
          style={{ top: 148 }}>
          <Menu
            className="small-menu"
            hidden={medicalRecordListQuery.isLoading || !isShowMenu}
            onClick={(e) => {
              // e.keyPath[1] is form type
              if (!e.keyPath[1]) {
                return
              }

              setSelectedMedicalRecordId(e.key)
              setSelectedFormType(e.keyPath[1])

              // hide menu when selecting a form
              // setIsShowMenu(false)
            }}
            selectedKeys={[selectedMedicalRecordId]}
            defaultOpenKeys={[...menu.map((r) => r.key)]}
            mode="inline"
            items={[...menu]}
          />
        </div>
        <div className="w-100">
          {selectedFormType === MEDICAL_RECORD_FORM_TYPE.PHIEU_DIEU_TRI.key ? (
            <TreatmentForm
              mainVisit={mainVisit}
              selectedVisit={selectedVisit}
              selectedMedicalRecordId={selectedMedicalRecordId}
            />
          ) : selectedFormType === MEDICAL_RECORD_FORM_TYPE.PHIEU_HOI_CHAN.key ? (
            <ConsultationForm
              mainVisit={mainVisit}
              selectedVisit={selectedVisit}
              selectedMedicalRecordId={selectedMedicalRecordId}
            />
          ) : selectedFormType === MEDICAL_RECORD_FORM_TYPE.BENH_AN_NGOAI_TRU.key ? (
            <OPDMedicalRecord
              mainVisit={mainVisit}
              selectedVisit={selectedVisit}
              selectedMedicalRecordId={selectedMedicalRecordId}
            />
          ) : selectedFormType === MEDICAL_RECORD_FORM_TYPE.GIAY_CHAP_THUAN.key ? (
            <ApprovalLetterForm
              mainVisit={mainVisit}
              selectedMedicalRecordId={selectedMedicalRecordId}
              patientId={patientId}
            />
          ) : selectedFormType === MEDICAL_RECORD_FORM_TYPE.PHIEU_CHI_DINH.key ? (
            <MedicalOrderForm
              mainVisit={mainVisit}
              selectedMedicalRecordId={selectedMedicalRecordId}
              patientId={patientId}
            />
          ) : selectedFormType ? (
            <CommonMedicalRecordForm
              key={selectedMedicalRecordId}
              mainVisit={mainVisit}
              selectedMedicalRecordId={selectedMedicalRecordId}
              formType={selectedFormType}
              editFields={getEditFieldsByFormType(selectedFormType)}
            />
          ) : (
            <div className="text-center">Chọn hồ sơ để xem chi tiết</div>
          )}
        </div>
      </div>

      <Modal
        title="TẠO MỚI PHIẾU"
        className="custom-modal"
        destroyOnClose
        open={openMedicalRecordForm.open}
        onCancel={() => setOpenMedicalRecordForm({})}
        footer={null}>
        <MedicalRecordForm
          patientId={patientId}
          patientVisitId={selectedVisit?.patient_visit_id}
          onSubmit={(newRecord) => {
            if (newRecord) {
              refetchMedicalRecord()
              setSelectedMedicalRecordId(newRecord.medical_record_form_id)
              setSelectedFormType(newRecord.medical_record_form_type_rcd)
            }
            setOpenMedicalRecordForm({})
          }}
        />
      </Modal>
    </>
  )
}

export default MedicalRecordList
