import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { getAllVisitChargeDetailsByPatientVisitId } from '../VisitService'
import { calculateChargeDetails } from '../VisitHelpers'
import nProgress from 'nprogress'
import { updateListItemService } from '../../../common/services'
import lists from '../../../common/lists'
import { MODE_VIEW_DATA } from '../../../common/constant'
import { useDeepCompareMemoize } from 'use-deep-compare-effect'
import { isEqual } from 'lodash'
import { displayDateTime, logDebug } from '../../../common/helpers'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../../store/auth'

// Keys for React Query
export const VISIT_CHARGE_QUERY_KEYS = {
  VISIT_CHARGE_DETAILS: 'visitChargeDetails',
}

export const useVisitChargeDetails = (selectedVisit, enabled = true) => {
  const queryClient = useQueryClient()

  const { modeViewData } = useSelector((state) => state[MODULE_AUTH])

  const visitChargeDetailKey = [
    VISIT_CHARGE_QUERY_KEYS.VISIT_CHARGE_DETAILS,
    selectedVisit?.patient_visit_id,
    modeViewData,
  ]

  const visitChargeDetailsQuery = useQuery({
    queryKey: visitChargeDetailKey,
    queryFn: async () => {
      const data = await getAllVisitChargeDetailsByPatientVisitId(selectedVisit?.patient_visit_id)

      if (!data?.value) {
        return []
      }

      let visitChargeDetails = data.value

      // manifulate charge
      visitChargeDetails = visitChargeDetails.map((item) => ({
        ...item,
        visit_dataset_detail: `${
          selectedVisit?.visit_code
        } - ${displayDateTime(selectedVisit?.actual_visit_datetime)}`,
      }))

      // auth by modeViewData
      if (modeViewData === MODE_VIEW_DATA.CASHIER) {
        visitChargeDetails = visitChargeDetails.filter(
          (item) => item.last_sent_cashier_date_time != null,
        )
      }

      // Calculate charge details
      return calculateChargeDetails(visitChargeDetails)
    },
    enabled: !!selectedVisit?.patient_visit_id && !!modeViewData && enabled,
    refetchInterval: 5 * 60 * 1000,
  })
  const isLoading = visitChargeDetailsQuery.isLoading
  const isSuccess = visitChargeDetailsQuery.isSuccess

  useEffect(
    () => {
      if (
        !selectedVisit?.patient_visit_id ||
        isLoading ||
        (visitChargeDetailsQuery.data?.find((r) => !!r.calculated) && false)
      ) {
        return
      }
      const calculatedTimes = visitChargeDetailsQuery.data?.[0]?.calculatedTimes || 0
      const oldData = visitChargeDetailsQuery.data
      const newData =
        visitChargeDetailsQuery.data?.length === 0
          ? []
          : calculateChargeDetails(visitChargeDetailsQuery.data || [])

      const clearCalc = (calcArray) => calcArray.map((r) => ({ ...r, calculatedTimes: 0 }))

      const getPriceOnly = (calcArray) =>
        calcArray.map((r) => ({
          ...r,
          health_insurance_price_with_ceiling: r.health_insurance_price_with_ceiling,
          ss_cover_raw: r.ss_cover_raw,
          ss_cover: r.ss_cover,
          health_insurance_amount: r.health_insurance_amount,
          BN_TU_CHI_TRA: r.BN_TU_CHI_TRA,
          TT_BN_CHI_TRA: r.TT_BN_CHI_TRA,
        }))

      if (!isEqual(clearCalc(oldData), clearCalc(newData))) {
        logDebug('NEED TO CHECK LOOP', getPriceOnly(oldData), getPriceOnly(newData))
        logDebug('calculatedTimes:', calculatedTimes)

        queryClient.setQueryData(visitChargeDetailKey, newData)
      }
    },
    useDeepCompareMemoize([
      selectedVisit?.patient_visit_id,
      visitChargeDetailsQuery.data,
      isLoading,
    ]),
  )

  const moveChargeDetailQuery = useMutation({
    mutationFn: async ({ isMoveUp, visit_charge_detail_id }) => {
      let ssCoverFlag = isMoveUp ? true : false

      const newData = {
        manual_ss_cover_flag: ssCoverFlag,
        last_sent_cashier_date_time: null,
        last_sent_cashier_user_id: null,
      }

      // Optimistically update UI
      queryClient.setQueryData(visitChargeDetailKey, (old) =>
        old?.map((item) =>
          item.visit_charge_detail_id === visit_charge_detail_id ? { ...item, newData } : item,
        ),
      )

      await updateListItemService(lists.visit_charge_detail, visit_charge_detail_id, newData)
    },
    onSuccess: () => {
      refetchData()
    },
  })

  useEffect(() => {
    if (isLoading) {
      nProgress.start()
    } else {
      nProgress.done()
    }

    return () => {
      nProgress.done()
    }
  }, [isLoading])

  // Manually refetch data
  const refetchData = async () => {
    queryClient.invalidateQueries({ queryKey: visitChargeDetailKey })
  }

  return {
    visitChargeDetailsQuery,
    data: visitChargeDetailsQuery.data || [],
    isLoading: isLoading,
    isSuccess: isSuccess,
    isError: visitChargeDetailsQuery.isError,
    moveChargeDetailQuery,
    refetchData,
  }
}
