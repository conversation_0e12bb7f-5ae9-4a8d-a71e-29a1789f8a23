import { Button, DatePicker, Form, Input, Popconfirm, Tabs } from 'antd'
import { useWatch } from 'antd/es/form/Form'
import { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import { useDebounceValue } from 'usehooks-ts'
import { XML_LIST } from '../../SI/constant'
import { findEntityPropertyByName, parseMetadata } from '../../SI/helper'
import PropTypes from '../../common/PropTypes'
import { useUI } from '../../common/UIProvider'
import COLOR from '../../common/color'
import DynamicTableComponent from '../../common/components/DynamicTable/DynamicTableComponent'
import { BUTTON_FONT_WEIGHT, FORMAT_DATE } from '../../common/constant'
import {
  generateDynamicTableColumns,
  generateDynamicTableDataSoruce,
  handleError,
} from '../../common/helpers'
import lists from '../../common/lists'
import {
  addListItemService,
  deleteListItemService,
  getItemsService,
  getMetadataService,
  updateListItemService,
} from '../../common/services'
import { useCommon } from '../../store/common'
import {
  checkXML,
  generateXMLTable,
  getSystemSettingTableColumnListByXmlType,
  sendXMLTableToThePortal,
} from './XmlToolService'
import dayjs from '../../common/dayjs'
import { ACTION_VISIT_HISTORY, PROCESSING_STATUS } from '../Visit/VisitConstant'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'
import { updateStatus } from '../Visit/VisitHelpers'
import { usePatientVisitHistory } from '../Visit/hooks/usePatientVisitHistory'
import AsyncButton from '../../common/components/AsyncButton'
import { XML_GATE_STATUS } from './XmlConstant'
import { PERMISSION } from '../Auth/AuthConstant'
import { useAuth } from '../../common/AuthProvider'
import { VISIT_QUERY_KEYS } from '../Visit/hooks/usePatientVisit'
import { useQueryClient } from '@tanstack/react-query'

const propTypes = {
  selectedSsTable1: PropTypes.object,
  currentPatientVisit: PropTypes.object,
  onBack: PropTypes.func,
  onSave: PropTypes.func,
  isCompactView: PropTypes.bool,
}

const DetailXMLToolPage = ({
  selectedSsTable1,
  currentPatientVisit, // it's mainVisit
  onBack,
  isCompactView,
  onSave = () => {},
}) => {
  //hooks
  const [tool] = Form.useForm()
  const ui = useUI()
  const { MetadataXml } = useCommon()
  const { invoice_no_ } = useParams()
  const { checkPermission } = useAuth()
  const queryClient = useQueryClient()
  // const
  const defaultTabKey = lists.ss_table_1.listName // Replace with your actual default tab key

  // Filter
  const filterHN = useDebounceValue(useWatch('filterHN', tool), 500)[0]
  const filterVisitDate = useWatch('filterVisitDate', tool)
  // state
  const [filterSSTable, setFilterSSTable] = useState(defaultTabKey)
  const [ssTable, setSSTable] = useState([])
  // eslint-disable-next-line no-unused-vars
  const [loadingSSTable, setLoadingSSTable] = useState(false)
  const [tableMetadataEntity, setTableMetadataEntity] = useState(null)
  const [systemSettingTableColumns, setSystemSettingTableColumns] = useState([])
  const [dataTableXmlTracking, setDataTableXmlTracking] = useState([])
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])
  const { addPatientVisitHistory, refetchHistory } = usePatientVisitHistory()

  // others
  const { RangePicker } = DatePicker
  const selectedInvoice_no_ = invoice_no_ || selectedSsTable1?.invoice_no_
  const selectedMA_LK = selectedSsTable1?.MA_LK
  const handleGetSSTableData = async (skip = '', top = 2000, withLoading = true) => {
    withLoading && setLoadingSSTable(true)
    ui.setLoading(true)

    // let ss_table = []
    let errorHandled = false // Flag to track if the error has been handled
    let data = []
    try {
      let filter = []
      if (selectedMA_LK) {
        filter.push(`(MA_LK eq '${selectedMA_LK}')`)
      }
      if (filterHN) {
        filter.push(`(MA_BN eq '${filterHN}')`)
      }
      if (filterVisitDate) {
        filter.push(
          `NGAY_VAO ge ${filterVisitDate[0].format(
            FORMAT_DATE,
          )} and NGAY_VAO le ${filterVisitDate[1].format(FORMAT_DATE)}`,
        )
      }
      filter.push(`include eq true`)
      if (
        filterSSTable == 'ss_table_1' ||
        filterSSTable == 'ss_table_2' ||
        filterSSTable == 'ss_table_3' ||
        filterSSTable == 'ss_table_4' ||
        filterSSTable == 'ss_table_5' ||
        filterSSTable == 'ss_table_7'
      ) {
        data = await getItemsService(lists[filterSSTable], {
          filter: filter.join('and '),
          top: top,
          skip: skip,
          //  orderBy: 'lu_updated desc'
        })
        setSSTable(data.value)
      } else {
        data = []
      }

      ui.setLoading(false)
      return data
    } catch (error) {
      if (!errorHandled) {
        handleError(error)
        ui.notiError('Không tìm thấy nội dung')
        errorHandled = true // Set the flag to true after handling the error
      }
      // setSSTable([])
    }
    setLoadingSSTable(false)
    ui.setLoading(false)
  }

  useEffect(() => {
    if (filterSSTable) {
      handleGetSSTableData()
    }
    handleGetDataTableXmlTracking()
  }, [filterSSTable, filterHN, filterVisitDate])

  const handleGetDataTableXmlTracking = async () => {
    let getDataTableXmlTracking = await getItemsService(lists.table_xml_tracking)
    if (getDataTableXmlTracking?.value?.length > 0) {
      setDataTableXmlTracking(getDataTableXmlTracking.value)
    } else {
      setDataTableXmlTracking([])
    }
  }

  const handleGenerateXMLTable = async () => {
    const filterInvoiceNo = selectedInvoice_no_
    await generateXMLTable({ filterSSTable, filterInvoiceNo, filterHN, filterVisitDate }, ui)
  }

  const handleSendXMLTableToThePortal = async (main_gate = 0) => {
    return await sendXMLTableToThePortal(selectedInvoice_no_, ui, main_gate)
  }

  const handleFetchTableMetadataEntity = async () => {
    let metadata_xml = MetadataXml
    if (!metadata_xml) {
      const response = await getMetadataService()
      metadata_xml = response.data
    }
    const parsedMetadata = await parseMetadata(metadata_xml)
    const entityType = findEntityPropertyByName(parsedMetadata, filterSSTable)
    setTableMetadataEntity(entityType)
    return entityType
  }
  const handleFetchSystemSettingTableColumns = async (xmlType) => {
    try {
      const columns = await getSystemSettingTableColumnListByXmlType(xmlType)
      setSystemSettingTableColumns(columns)
    } catch (error) {
      handleError(error)
    }
  }
  useEffect(() => {
    handleFetchTableMetadataEntity()
    handleFetchSystemSettingTableColumns(lists[filterSSTable].xmlType)
    //handleFetchTableMetadataEntity()
  }, [filterSSTable])
  const handleOnSaveDetailXmlToolTable = async () => {
    handleGetSSTableData()
    // return null
  }
  const handleConfirmProcessed = async () => {
    try {
      if (
        (dataTableXmlTracking?.table_1_id ?? '') === selectedSsTable1?.table_1_id &&
        (dataTableXmlTracking?.table_name ?? '') === tableMetadataEntity?.['@_Name']
      ) {
        let getTableXmlTrackingId = await getItemsService(lists.table_xml_tracking, {
          filter: `table_1_id eq '${selectedSsTable1?.table_1_id}' and table_name eq '${tableMetadataEntity?.['@_Name']}'`,
          top: 1,
        }).then((res) => res.value[0])

        if (getTableXmlTrackingId.length > 0) {
          await updateListItemService(
            lists.table_xml_tracking,
            getTableXmlTrackingId?.table_xml_tracking_id,
            {
              table_1_id: selectedSsTable1?.table_1_id,
              table_name: tableMetadataEntity?.['@_Name'],
            },
          )
        }
      } else {
        await addListItemService(lists.table_xml_tracking, {
          table_1_id: selectedSsTable1?.table_1_id,
          table_name: tableMetadataEntity?.['@_Name'],
          status: 'approved',
          active_flag: true,
          lu_updated: dayjs(),
        })
      }

      // await updateStatus(dataTableXmlTracking, selectedSsTable1)
      await updateListItemService(lists.ss_table_1, selectedSsTable1?.table_1_id, {
        approve_status: 'approved',
        gate_status: XML_GATE_STATUS.WAITING_SUBMIT.key,
      })

      await updateListItemService(lists.patient_visit, currentPatientVisit.patient_visit_id, {
        processing_status: PROCESSING_STATUS.XML_PROCESSED.name_e,
      })

      //lưu vào lịch sử
      const historyData = {
        patient_visit_id: currentPatientVisit?.patient_visit_id,
        note: 'MA_LK: ' + selectedSsTable1?.MA_LK,
      }

      addPatientVisitHistory.mutateAsync({
        historyData,
        action: ACTION_VISIT_HISTORY.CONFIRM_XML,
      })

      ui.notiSuccess('Xác nhận đã xử lý thành công')
      // onBack()
      // onSave()
    } catch (error) {
      handleError(error)
    }
  }

  const isDataAvailable = generateDynamicTableDataSoruce(ssTable).length > 0
  // const handleGetSystemSettingTableColumnList = async () => {
  //   let system_setting_table_columns = systemSettingTableColumnList
  //   if (!system_setting_table_columns) {
  //     system_setting_table_columns = await getSystemSettingTableColumnList()
  //     dispatch(siXmlToolActions.setSystemSettingTableColumnList(system_setting_table_columns))
  //   }
  //   setIsSystemSettingTableColumnListFetched(true)
  // }
  // useEffect(() => {
  //   handleGetSystemSettingTableColumnList()
  // }, [])

  const content = (
    <>
      <div className="container-fluid">
        <div className="d-flex justify-content-md-start gap-2 align-items-center">
          {/* {selectedSsTable1 && (
            <Button onClick={onBack} icon={<i className="fa-solid fa-circle-left"></i>}>
              Quay lại
            </Button>
          )} */}
          <Button onClick={onBack} icon={<i className="fa-solid fa-circle-left"></i>}>
            Quay lại
          </Button>
          <span className="fw-bold"> DetailXMLTool </span>
          <span>{selectedInvoice_no_}</span>
          {!selectedSsTable1 && <Button onClick={() => {}}>GET FORM VALUE </Button>}
        </div>
        <hr className="row mt-2"></hr>

        <div className="row mt-2">
          <div className="d-flex justify-content-md-start align-items-baseline gap-2  ">
            <span>Tìm kiếm: </span>
            <Form.Item name={'filterSSTable'}>
              <Tabs
                defaultActiveKey={defaultTabKey}
                onChange={(key) => setFilterSSTable(key)}
                items={XML_LIST.map((item) => ({
                  key: item.list_name,
                  label: item.displayName,
                  //value: item.list_name
                }))}></Tabs>
            </Form.Item>
          </div>
          {!isCompactView && (
            <div className="d-flex justify-content-md-start gap-2">
              <Form.Item label="HN:" name={'filterHN'}>
                <Input allowClear placeholder="Search hn"></Input>
              </Form.Item>
              <Form.Item label="Visit date:" name={'filterVisitDate'}>
                <RangePicker></RangePicker>
              </Form.Item>
            </div>
          )}
        </div>
        <div className="row mt-2">
          <div className="d-flex justify-content-md-start gap-3">
            <>
              {!isCompactView && (
                <>
                  <Button
                    onClick={async () => {
                      handleGenerateXMLTable()
                    }}>
                    GENERATE DATA
                  </Button>
                  <Button
                    onClick={async () => {
                      await handleSendXMLTableToThePortal()
                    }}>
                    EXPORT XML
                  </Button>
                </>
              )}
              <Button
                onClick={() => {
                  handleGetSSTableData()
                }}
                style={{
                  backgroundColor: 'white',
                  color: COLOR.lime,
                  borderColor: COLOR.lime,
                }}>
                Làm mới dữ liệu
              </Button>
              {isDataAvailable && (
                <AsyncButton onClick={handleConfirmProcessed} variant="solid" color="blue">
                  Xác nhận đã xử lý
                </AsyncButton>
              )}
            </>
          </div>
        </div>
        <hr className="row mt-2"></hr>
        <div className="row mt-2"></div>
        {ssTable.length > 0 && (
          <>
            <DynamicTableComponent
              canNavigateToDetailPage={false}
              isEditableTable={true}
              tableColumns={generateDynamicTableColumns(ssTable, systemSettingTableColumns)}
              tableDataSource={generateDynamicTableDataSoruce(ssTable)}
              tableMetadataEntity={tableMetadataEntity}
              tablePrimaryKeyName={tableMetadataEntity?.Key?.PropertyRef['@_Name']}
              tableName={tableMetadataEntity?.['@_Name']}
              onSave={handleOnSaveDetailXmlToolTable}></DynamicTableComponent>
          </>
        )}
        {/* {filterSSTable && (
          <SPPagination
            getItems={handleGetSSTableData}
            setItems={setSSTable}
            items={ssTable}
            loading={loadingSSTable}
            setLoading={setLoadingSSTable}></SPPagination>
        )} */}
        <div className="row mt-2">
          <div className="d-flex justify-content-md-end gap-2">
            <Button onClick={onBack} icon={<i className="fa-solid fa-circle-left"></i>}>
              Quay lại
            </Button>
            <Popconfirm
              title="Bạn có chắc chắn muốn xóa?"
              okText="Yes"
              cancelText="No"
              onConfirm={async () => {
                await deleteListItemService(lists.ss_table_1, selectedSsTable1?.table_1_id)
                //lưu vào lịch sử
                const historyData = {
                  patient_visit_id: currentPatientVisit?.patient_visit_id,
                  note: 'MA_LK: ' + selectedSsTable1?.MA_LK,
                }

                addPatientVisitHistory.mutateAsync({
                  historyData,
                  action: ACTION_VISIT_HISTORY.DELETE_XML,
                })
                handleGetSSTableData()
              }}>
              {' '}
              <Button
                style={{ borderColor: COLOR.red, color: 'black', fontWeight: BUTTON_FONT_WEIGHT }}>
                Hủy bảng xml
              </Button>
            </Popconfirm>

            {/* <Button
              style={{ borderColor: COLOR.red, color: 'black', fontWeight: BUTTON_FONT_WEIGHT }}
              onClick={() => {
                deleteListItemService(lists.ss_table_1, selectedSsTable1?.table_1_id)
                onSave()
              }}>
              Huỷ bảng xml
            </Button> */}
            <Button
              variant="solid"
              color="cyan"
              onClick={async () => {
                if (!checkXML([selectedSsTable1], ui)) {
                  return
                }

                //lưu vào lịch sử
                addPatientVisitHistory.mutateAsync({
                  historyData: {
                    patient_visit_id: currentPatientVisit?.patient_visit_id,
                    note: `MA_LK: ${selectedSsTable1?.MA_LK}; T_BHTT: ${selectedSsTable1?.T_BHTT}`,
                  },
                  action: ACTION_VISIT_HISTORY.SUBMIT_XML,
                })

                const res = await handleSendXMLTableToThePortal(0)

                if (!res.isSuccess) {
                  ui.notiError('Đẩy cổng thất bại', res?.message)
                  return
                }

                await updateListItemService(
                  lists.patient_visit,
                  currentPatientVisit?.patient_visit_id,
                  {
                    processing_status: PROCESSING_STATUS.SENT_TO_GATEWAY.name_e,
                  },
                )

                // refetch main visit
                queryClient.invalidateQueries({
                  queryKey: [VISIT_QUERY_KEYS.PATIENT_VISIT, currentPatientVisit?.patient_visit_id],
                })

                onSave()
              }}>
              Đẩy cổng
            </Button>
            <Button
              hidden={!checkPermission(PERMISSION.SUBMIT_TO_GATE)}
              variant="solid"
              color="cyan"
              disabled={
                !checkPermission(PERMISSION.SUBMIT_TO_GATE_UNLIMITED) &&
                currentPatientVisit?.processing_status === PROCESSING_STATUS.SENT_TO_GATEWAY.name_e
              }
              onClick={async () => {
                if (!checkXML([selectedSsTable1], ui)) {
                  return
                }

                //lưu vào lịch sử
                addPatientVisitHistory.mutateAsync({
                  historyData: {
                    patient_visit_id: currentPatientVisit?.patient_visit_id,
                    note: `MA_LK: ${selectedSsTable1?.MA_LK}; T_BHTT: ${selectedSsTable1?.T_BHTT}`,
                  },
                  action: ACTION_VISIT_HISTORY.SUBMIT_XML,
                })

                const res = await handleSendXMLTableToThePortal(1)

                if (!res.isSuccess) {
                  ui.notiError('Đẩy cổng thất bại', res?.message)
                  return
                }

                await updateListItemService(
                  lists.patient_visit,
                  currentPatientVisit.patient_visit_id,
                  {
                    processing_status: PROCESSING_STATUS.SENT_TO_GATEWAY.name_e,
                  },
                )

                // refetch main visit
                queryClient.invalidateQueries({
                  queryKey: [VISIT_QUERY_KEYS.PATIENT_VISIT, currentPatientVisit?.patient_visit_id],
                })
                refetchHistory()

                onSave()
              }}>
              Đẩy cổng chính
            </Button>
          </div>
        </div>
      </div>
    </>
  )

  if (!ssTable) {
    return <div>Loading...</div>
  }

  return <Form form={tool}>{content}</Form>
}

DetailXMLToolPage.propTypes = propTypes

export default DetailXMLToolPage
