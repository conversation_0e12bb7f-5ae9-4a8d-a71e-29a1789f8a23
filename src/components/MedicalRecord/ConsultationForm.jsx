import { Button, DatePicker, Form, Switch, Select, Tag, Row, Col } from 'antd'
import React, { useState } from 'react'
import { useSelector } from 'react-redux'
import { useQueryClient } from '@tanstack/react-query'
import { useForm } from 'antd/es/form/Form'
import { useVisitChargeDetails } from '../Visit/hooks/useVisitChargeDetails'
import useDeepCompareEffect from 'use-deep-compare-effect'
import { useMedicalRecord, MEDICAL_RECORD_KEYS } from '../../queryHooks/useMedicalRecord'
import { handleError } from '../../common/helpers'
import { updateListItemService } from '../../common/services'
import lists from '../../common/lists'
import AsyncButton from '../../common/components/AsyncButton'
import { FORM_MODE } from '../../common/constant'
import MedicalRecordField, { MEDICAL_RECORD_FIELD_TYPE } from './MedicalRecordField'
import useApp from 'antd/es/app/useApp'
import DocumentStore from '../../common/components/DocumentStore/DocumentStore'
import PdfViewer from '../../common/components/PdfViewer'
import dayjs from '../../common/dayjs'
import { usePatientVisit } from '../Visit/hooks/usePatientVisit'
import fvLogoWithText from '../../assets/logoFV-withtext.png'
import { MODULE_AUTH } from '../../store/auth'
import { useEmployee } from '../../queryHooks/useEmployee'
import { usePatientMedicalCoding } from '../Visit/hooks/usePatientMedicalCoding'
import { useDepartmentMapping } from '../../queryHooks/useDepartmentMapping'
import { handlePrintPDF } from '../../SI/helper'

// No template options needed

// test: http://localhost:5173/si/his/visit/6cb5a87d-ea1c-4034-ad41-08dcbcfeb145?selectedTabKey=medicalRecords&modeViewData=NORMAL&visitDetailMode=edit&selectedPatientVisitId=null
// Each consultation form is linked to a specific medical_condition record (XML5 data)
const ConsultationForm = ({ selectedVisit, mainVisit, selectedMedicalRecordId }) => {
  const [form] = useForm()
  const app = useApp()
  const queryClient = useQueryClient()
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])

  // hooks
  const { data: visitChargeDetailsData } = useVisitChargeDetails(selectedVisit)
  const { medicalRecordDetail, medicalCondition } = useMedicalRecord({
    medicalRecordFormId: selectedMedicalRecordId,
  })
  const { employee: luUserInfo } = useEmployee({ userId: medicalRecordDetail?.lu_user_id })
  const { patientMedicalCodingList } = usePatientMedicalCoding(selectedVisit?.patient_visit_id)
  const { departmentMappings } = useDepartmentMapping()
  const primaryMedicalCoding = patientMedicalCodingList?.find((item) => item.primary_flag)

  const [formMode, setFormMode] = useState(FORM_MODE.view)
  const isEdit = formMode === FORM_MODE.edit
  const [openDocumentStore, setOpenDocumentStore] = useState(false)
  const [attachments, setAttachments] = useState([])
  const [viewFileOnly, setViewFileOnly] = useState()

  // Tag handling is now managed by the MedicalRecordField component

  // default values
  useDeepCompareEffect(() => {
    // If we have a linked medical_condition, use its HOI_CHAN field for the disease_and_treatment_summary
    // if it's not already set in the medical record form
    const diseaseAndTreatmentSummary =
      medicalRecordDetail?.disease_and_treatment_summary || medicalCondition?.HOI_CHAN || ''

    // Parse consultation date time for hour and minute selectors
    const consultationTime = medicalRecordDetail?.consultation_date_time
      ? dayjs(medicalRecordDetail.consultation_date_time)
      : dayjs()

    // Parse participant lists into arrays for form fields
    const chairpersonList = medicalRecordDetail?.chairperson_full_name_list || ''
    const secretaryList = medicalRecordDetail?.secretary_full_name_list || ''
    const participantList = medicalRecordDetail?.participant_full_name_list || ''

    // Convert string lists to arrays for form fields
    const chairpersonArray = chairpersonList
      ? chairpersonList
          .split(',')
          .map((name) => name.trim())
          .filter(Boolean)
      : []

    const secretaryArray = secretaryList
      ? secretaryList
          .split(',')
          .map((name) => name.trim())
          .filter(Boolean)
      : []

    const participantArray = participantList
      ? participantList
          .split(',')
          .map((name) => name.trim())
          .filter(Boolean)
      : []

    form.setFieldsValue({
      consultationDate: consultationTime,
      consultationHour: consultationTime.format('HH'),
      consultationMinute: consultationTime.format('mm'),
      chairpersonFullNameList: chairpersonArray,
      secretaryFullNameList: secretaryArray,
      participantFullNameList: participantArray,
      diseaseAndTreatmentSummary: diseaseAndTreatmentSummary,
      conclusion: medicalRecordDetail?.conclusion || '',
      treatmentRecommendation: medicalRecordDetail?.treatment_recommendation || '',
      chairperson: medicalRecordDetail?.chairperson || '',
      signedDateTime: medicalRecordDetail?.signed_date_time
        ? dayjs(medicalRecordDetail.signed_date_time)
        : dayjs(),
      departmentId: medicalRecordDetail?.department_id || selectedVisit?.department_id || '',
    })
  }, [medicalRecordDetail, medicalCondition, form, selectedVisit])

  // Extract distinct doctor names from charge details and use as options for all participant fields
  const [doctorOptions, setDoctorOptions] = useState([])

  useDeepCompareEffect(() => {
    if (!visitChargeDetailsData[0]) {
      return
    }

    // Get distinct doctor names from charge details
    const distinctDoctorNames = [
      ...new Set(visitChargeDetailsData.map((item) => item.doctor_name).filter(Boolean)),
    ]

    // Update doctor options for all Select components
    setDoctorOptions(distinctDoctorNames)

    // Only auto-fill participant list if it's empty
    if (distinctDoctorNames.length > 0 && !form.getFieldValue('participantFullNameList')) {
      form.setFieldsValue({
        participantFullNameList: distinctDoctorNames,
      })
    }
  }, [visitChargeDetailsData, form])

  const handleSaveMedicalRecordForm = async () => {
    if (!selectedMedicalRecordId) {
      app.message.error('Vui lòng chọn phiếu hội chẩn')
      return
    }

    const values = form.getFieldsValue()

    try {
      // Combine date and time fields
      const consultationDate = values.consultationDate || dayjs()
      const hour = values.consultationHour || consultationDate.format('HH')
      const minute = values.consultationMinute || consultationDate.format('mm')

      // Create a new date with the combined values
      const consultationDateTime = dayjs(consultationDate)
        .hour(parseInt(hour, 10))
        .minute(parseInt(minute, 10))
        .toISOString()

      // Get values from form and convert to comma-separated strings if needed
      const chairpersonFullNameList = Array.isArray(values.chairpersonFullNameList)
        ? values.chairpersonFullNameList.join(', ')
        : values.chairpersonFullNameList || ''

      const secretaryFullNameList = Array.isArray(values.secretaryFullNameList)
        ? values.secretaryFullNameList.join(', ')
        : values.secretaryFullNameList || ''

      const participantFullNameList = Array.isArray(values.participantFullNameList)
        ? values.participantFullNameList.join(', ')
        : values.participantFullNameList || ''

      const newRecord = {
        consultation_date_time: consultationDateTime,
        chairperson_full_name_list: chairpersonFullNameList,
        secretary_full_name_list: secretaryFullNameList,
        participant_full_name_list: participantFullNameList,
        disease_and_treatment_summary: values.diseaseAndTreatmentSummary,
        conclusion: values.conclusion,
        treatment_recommendation: values.treatmentRecommendation,
        chairperson: values.chairperson,
        department_id: values.departmentId,
        lu_user_id: currentUser?.User_id,
      }

      await updateListItemService(lists.medical_record_form, selectedMedicalRecordId, newRecord)

      // If this consultation form is linked to a medical_condition, update the HOI_CHAN field
      if (medicalCondition?.medical_condition_id && medicalRecordDetail?.medical_condition_id) {
        await updateListItemService(
          lists.medical_condition,
          medicalCondition.medical_condition_id,
          {
            HOI_CHAN: values.diseaseAndTreatmentSummary,
          },
        )
      }

      // Tag states are already updated through the handlers, no need to update them here

      // Invalidate the query cache to force a refresh of the medical record data
      queryClient.invalidateQueries({
        queryKey: [MEDICAL_RECORD_KEYS.MEDICAL_RECORD, selectedMedicalRecordId],
      })

      app.message.success('Lưu thành công')
    } catch (error) {
      handleError(error, 'handleSaveMedicalRecordForm')
    }
  }

  // Tag handling is now managed by the MedicalRecordField component

  return (
    <div>
      <div
        className="sticky-top d-flex justify-content-between align-items-center gap-2"
        style={{ top: 105 }}>
        <div></div>
        <div className="d-flex align-items-center gap-2">
          {/* toggle to view file or form */}
          <div className="d-flex align-items-center me-2 gap-2">
            <Switch onChange={() => setViewFileOnly(!viewFileOnly)} checked={viewFileOnly} /> Chỉ
            xem File đính kèm
          </div>
          <Button
            variant={isEdit ? 'outlined' : 'solid'}
            color={'blue'}
            icon={<i className="fa fa-edit" />}
            onClick={() => setFormMode(isEdit ? FORM_MODE.view : FORM_MODE.edit)}>
            {isEdit ? 'Tắt Chỉnh sửa' : 'Chỉnh sửa'}
          </Button>
          <AsyncButton
            icon={<i className="fa fa-save" />}
            hidden={!isEdit}
            onClick={handleSaveMedicalRecordForm}>
            Lưu
          </AsyncButton>
          <Button
            type="primary"
            style={{ background: '#2C9538' }}
            icon={
              openDocumentStore ? <i className="fa fa-close" /> : <i className="fa fa-upload" />
            }
            onClick={() => setOpenDocumentStore(!openDocumentStore)}>
            {openDocumentStore ? 'Đóng' : 'Mở'} upload
          </Button>
          <Button
            icon={<i className="fa fa-print" />}
            type="primary"
            style={{ backgroundColor: '#155E75' }}
            onClick={() => {
              setViewFileOnly(false)
              setOpenDocumentStore(false)
              setFormMode(FORM_MODE.view)

              handlePrintPDF(`PhieuHoiChan_${medicalRecordDetail?.title || ''}`)
            }}>
            In phiếu
          </Button>
        </div>
      </div>

      <div
        className="mt-2 mb-4 shadow-md p-3 rounded-sm"
        style={{ display: openDocumentStore ? 'block' : 'none' }}>
        <DocumentStore
          dataSource={lists.medical_record_form.listName}
          parentID={4} // 4 is DocumentStore
          storeID={selectedMedicalRecordId}
          mode={'Edit'}
          setAttachments={setAttachments}
        />
      </div>

      <div id="medical-record-form-print" hidden={viewFileOnly} className="mt-3 px-4">
        <Form form={form} layout="vertical">
          <div className="mb-4">
            <div>
              <img
                src={fvLogoWithText}
                alt="FV Hospital"
                style={{ height: '40px', marginBottom: '10px' }}
                onError={(e) => {
                  e.target.style.display = 'none'
                  e.target.nextSibling.style.display = 'block'
                }}
              />
              <div style={{ display: 'none' }}>FV THOMSON</div>
            </div>
            <h4 className="text-center fw-bold">BIÊN BẢN HỘI CHẨN</h4>
          </div>

          {/* Patient Information */}
          <Row gutter={24}>
            <Col span={14}>
              <div className="mb-2">Họ tên: {mainVisit?.fullname || ''}</div>
              <div className="mb-2">
                Ngày sinh: {mainVisit?.dob ? dayjs(mainVisit.dob).format('DD/MM/YYYY') : ''}
              </div>
              <div className="mb-2">
                Điều trị ngày:{' '}
                {selectedVisit?.actual_visit_datetime
                  ? dayjs(selectedVisit.actual_visit_datetime).format('DD/MM/YYYY')
                  : ''}
              </div>
              <div className="mb-2 d-flex gap-2 align-items-center">
                Phòng:
                <MedicalRecordField
                  form={form}
                  formMode={formMode}
                  fieldName="room"
                  fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXT}
                  underlineLabel={true}
                />
              </div>
              <div className="mb-2">Chẩn đoán: {primaryMedicalCoding?.TEN_BENH_CHINH || ''} </div>
            </Col>

            <Col span={10}>
              <div className="mb-2">HN: {mainVisit?.visible_patient_id || ''}</div>
              <div className="mb-2">Giới tính: {mainVisit?.sex === 'Male' ? 'Nam' : 'Nữ'}</div>
              <div className="mb-2">
                đến ngày:{' '}
                {selectedVisit?.NGAY_RA ? dayjs(selectedVisit.NGAY_RA).format('DD/MM/YYYY') : ''}
              </div>
              <div className="mb-2 d-flex gap-2 align-items-center">
                <div>Khoa:</div>
                <div style={{ minWidth: '200px' }}>
                  <MedicalRecordField
                    form={form}
                    formMode={formMode}
                    fieldName="departmentId"
                    fieldType={MEDICAL_RECORD_FIELD_TYPE.SELECT}
                    selectOptions={departmentMappings.map((dept) => ({
                      value: dept.department_id,
                      label: dept.ten_khoa_vn || dept.ten_khoa_en,
                    }))}
                  />
                </div>
              </div>
            </Col>
          </Row>

          <div>
            <Row gutter={24} className="mb-3">
              <Col span={14}>
                <div className="d-flex gap-2 align-items-center">
                  <div>Hội chẩn lúc</div>
                  <div className="d-flex gap-1 align-items-center">
                    <Form.Item name="consultationHour" noStyle>
                      {isEdit ? (
                        <Select placeholder="chọn giờ" style={{ width: '100%' }}>
                          {Array.from({ length: 24 }, (_, i) => i).map((hour) => (
                            <Select.Option key={hour} value={hour.toString().padStart(2, '0')}>
                              {hour.toString().padStart(2, '0')}
                            </Select.Option>
                          ))}
                        </Select>
                      ) : (
                        <span>{form.getFieldValue('consultationHour')}</span>
                      )}
                    </Form.Item>
                    giờ
                  </div>
                  <div className="d-flex gap-1 align-items-center">
                    <Form.Item name="consultationMinute" noStyle>
                      {isEdit ? (
                        <Select placeholder="chọn phút" style={{ width: '100%' }}>
                          {Array.from({ length: 60 }, (_, i) => i).map((minute) => (
                            <Select.Option key={minute} value={minute.toString().padStart(2, '0')}>
                              {minute.toString().padStart(2, '0')}
                            </Select.Option>
                          ))}
                        </Select>
                      ) : (
                        <span>{form.getFieldValue('consultationMinute')}</span>
                      )}
                    </Form.Item>
                    phút,
                  </div>
                </div>
              </Col>

              <Col span={10} className="d-flex gap-2 align-items-center">
                <div className="w-[80px]">Ngày:</div>
                <Form.Item name="consultationDate" noStyle>
                  {isEdit ? (
                    <DatePicker format="DD/MM/YYYY" style={{ width: '100%' }} />
                  ) : (
                    <span>{form.getFieldValue('consultationDate')?.format('DD/MM/YYYY')}</span>
                  )}
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24} className="mb-3">
              <Col span={14}>
                <MedicalRecordField
                  form={form}
                  formMode={formMode}
                  fieldName="chairpersonFullNameList"
                  label="Chủ trì:"
                  fieldType={MEDICAL_RECORD_FIELD_TYPE.TAG_SELECT}
                  tagFieldName="employee_name"
                  lazySelectProps={{
                    list: lists.employee_dataset,
                    searchFields: ['employee_name', 'MA_CCHN'],
                    keyProp: 'employee_name',
                    placeholder: 'Tìm và chọn',
                    className: 'w-[300px]',
                    renderOption: (item) => (
                      <Select.Option
                        item={item}
                        value={item.employee_name}
                        key={item.employee_name}>
                        {item.employee_name}
                      </Select.Option>
                    ),
                  }}
                />
              </Col>
              <Col span={10}>
                <MedicalRecordField
                  form={form}
                  formMode={formMode}
                  fieldName="secretaryFullNameList"
                  label="Thư ký:"
                  fieldType={MEDICAL_RECORD_FIELD_TYPE.TAG_SELECT}
                  tagFieldName="employee_name"
                  lazySelectProps={{
                    list: lists.employee_dataset,
                    searchFields: ['employee_name', 'MA_CCHN'],
                    keyProp: 'employee_name',
                    placeholder: 'Tìm và chọn',
                    className: 'w-[300px]',
                    renderOption: (item) => (
                      <Select.Option
                        item={item}
                        value={item.employee_name}
                        key={item.employee_name}>
                        {item.employee_name}
                      </Select.Option>
                    ),
                  }}
                />
              </Col>
            </Row>

            <div className="mb-3">
              <MedicalRecordField
                form={form}
                formMode={formMode}
                fieldName="participantFullNameList"
                label="Thành viên tham gia:"
                fieldType={MEDICAL_RECORD_FIELD_TYPE.TAG_SELECT}
                tagFieldName="employee_name"
                lazySelectProps={{
                  list: lists.employee_dataset,
                  searchFields: ['employee_name', 'MA_CCHN'],
                  keyProp: 'employee_name',
                  placeholder: 'Tìm và chọn',
                  className: 'w-[300px]',
                  renderOption: (item) => (
                    <Select.Option item={item} value={item.employee_name} key={item.employee_name}>
                      {item.employee_name}
                    </Select.Option>
                  ),
                }}
              />
            </div>

            <div className="my-2">
              <MedicalRecordField
                form={form}
                formMode={formMode}
                fieldName="diseaseAndTreatmentSummary"
                additionalButtons={
                  <Button
                    icon={<i className="fa-regular fa-square-plus"></i>}
                    size="small"
                    onClick={() => {
                      if (!medicalCondition?.HOI_CHAN) {
                        app.message.warning('Chưa có')
                        return
                      }

                      form.setFieldsValue({
                        diseaseAndTreatmentSummary: medicalCondition?.HOI_CHAN,
                      })
                    }}>
                    Gán lại từ dữ liệu bảng XML5
                  </Button>
                }
                renderValue={(value) => {
                  if (!value) {
                    return ''
                  }

                  // replace to bold
                  const textNeedToBold = [
                    'Tóm tắt quá trình diễn biến bệnh, điều trị và chăm sóc người bệnh:',
                    'Tóm tắt kết quả cận lâm sàng:',
                    'Kết luận:',
                    'Hướng điều trị:',
                  ]

                  textNeedToBold.forEach((text) => {
                    value = value.replaceAll(text, `<b>${text}</b>`)
                  })

                  return <pre dangerouslySetInnerHTML={{ __html: value }} />
                }}
              />
            </div>

            {/* Footer section with treatment recommendation and signatures */}
            <div className="mt-3">
              <Row gutter={24}>
                <Col span={14}>
                  <div>
                    <MedicalRecordField
                      form={form}
                      formMode={formMode}
                      fieldName="signed_date_time"
                      fieldType={MEDICAL_RECORD_FIELD_TYPE.DATE}
                      underlineLabel={false}
                    />
                  </div>
                  <div>Thư ký</div>
                  <div style={{ marginTop: 100 }}>{luUserInfo?.employee_name || ''}</div>
                </Col>

                <Col span={10}>
                  <div>Chủ tọa</div>
                  <div style={{ marginTop: 122 }}></div>
                  <Form.Item name="chairperson" noStyle>
                    {isEdit ? (
                      <Select
                        style={{ width: '100%' }}
                        placeholder="chọn Chủ tọa"
                        value={form.getFieldValue('chairperson')}
                        onChange={(value) => {
                          form.setFieldsValue({ chairperson: value })
                        }}>
                        {/* Use chairpersonFullNameList as options */}
                        {(form.getFieldValue('chairpersonFullNameList') || []).map((doctor) => (
                          <Select.Option key={doctor} value={doctor}>
                            {doctor}
                          </Select.Option>
                        ))}
                        {/* Also include doctorOptions that aren't in chairpersonFullNameList */}
                        {doctorOptions
                          .filter(
                            (doctor) =>
                              !(form.getFieldValue('chairpersonFullNameList') || []).includes(
                                doctor,
                              ),
                          )
                          .map((doctor) => (
                            <Select.Option key={doctor} value={doctor}>
                              {doctor}
                            </Select.Option>
                          ))}
                      </Select>
                    ) : (
                      <div>{form.getFieldValue('chairperson')}</div>
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </div>
          </div>
        </Form>
      </div>

      <div hidden={!viewFileOnly} className="mt-3">
        <PdfViewer
          serverRelativeUrl={attachments[0]?.ServerRelativeUrl}
          fileName={attachments[0]?.Name}
        />
      </div>
    </div>
  )
}

export default ConsultationForm
