import { Button, DatePicker, Form, Input, Modal, Select, Tag } from 'antd'
import React, { useState, useEffect } from 'react'
import { FORM_MODE } from '../../common/constant'
import SelectFromTemplateText from './SelectFromTemplateText'
import { useWatch } from 'antd/es/form/Form'
import dayjs from '../../common/dayjs'
import LazySelect from '../../common/components/LazySelect'
import PropTypes from '../../common/PropTypes'
import { filterOption } from '../../common/helpers'
import { getItemsService } from '../../common/services'
import { useDepartmentMapping } from '../../queryHooks/useDepartmentMapping'

export const MEDICAL_RECORD_FIELD_TYPE = {
  TEXT: 'TEXT',
  TEXTAREA: 'TEXTAREA',
  NUMBER: 'NUMBER',
  SELECT: 'SELECT',
  TAG_SELECT: 'TAG_SELECT',
  DATE: 'DATE',
  DATETIME: 'DATETIME',
  TIME: 'TIME',
}

const propTypes = {
  form: PropTypes.object,
  templateOptions: PropTypes.array,
  formMode: PropTypes.oneOf(['edit', 'view']),
  label: PropTypes.string,
  fieldName: PropTypes.string,
  fieldType: PropTypes.oneOf(Object.values(MEDICAL_RECORD_FIELD_TYPE)),
  customField: PropTypes.node,
  renderValue: PropTypes.func,
  selectOptions: PropTypes.array,
  // label
  underlineLabel: PropTypes.bool,
  labelBold: PropTypes.bool,
  labelClassName: PropTypes.string,

  required: PropTypes.bool,
  hidden: PropTypes.bool,
  controlProps: PropTypes.object,
  additionalButtons: PropTypes.node,
  tagFieldName: PropTypes.string,
  tagFieldNameForDisplay: PropTypes.string,
  lazySelectProps: PropTypes.object,
}

const MedicalRecordField = ({
  form,
  templateOptions,
  formMode,
  label,
  fieldName,
  fieldType = MEDICAL_RECORD_FIELD_TYPE.TEXTAREA,
  customField,
  renderValue,
  selectOptions = [],
  // label
  underlineLabel = false,
  labelBold = true,
  labelClassName = '',

  required = false,
  hidden = false,
  controlProps = {},
  additionalButtons,
  tagFieldName,
  tagFieldNameForDisplay,
  lazySelectProps = {},
  ...rest
} = {}) => {
  const [isOpenSelectTextModal, setIsOpenSelectTextModal] = useState(false)
  const [selectedTemplateOptionContext, setSelectedTemplateOptionContext] = useState({
    fieldName: '',
    options: [],
  })
  const isEdit = formMode === FORM_MODE.edit

  // State for tag-based selection
  const [tags, setTags] = useState([])
  const [displayTags, setDisplayTags] = useState([])

  const fieldValue = !!fieldName && !!form ? useWatch(fieldName, form) : undefined

  const antdProps = {
    required,
    hidden,
    ...rest,
  }

  const rootControlProps = {
    ...controlProps,
    style: {
      ...controlProps.style,
      marginTop: 5,
      marginBottom: 5,
    },
  }

  // settings
  let settingSelectOptions = selectOptions
  // for department_id
  const isDepartmentIdField = fieldName === 'departmentId' || fieldName === 'department_id'
  const { departmentMappings } = useDepartmentMapping({
    enabled: isDepartmentIdField,
  })
  if (isDepartmentIdField) {
    settingSelectOptions = departmentMappings.map((dept) => ({
      value: dept.department_id,
      label: dept.ten_khoa_vn || dept.ten_khoa_en,
    }))
  }

  const handleRemoveTag = (tagToRemove) => {
    const newTags = tags.filter((tag) => tag !== tagToRemove)
    setTags(newTags)

    form.setFieldsValue({
      [fieldName]: newTags,
    })
  }

  const handleAddTag = (item) => {
    if (item) {
      const tagText = item[tagFieldName]

      if (!tags.some((tag) => tag === tagText)) {
        const newTags = [...tags, tagText]

        setTags(newTags)

        form.setFieldsValue({
          [fieldName]: newTags,
        })
      }
    }
  }

  // init tags
  useEffect(() => {
    const initTags = async () => {
      if (fieldValue && Array.isArray(fieldValue)) {
        setTags([...fieldValue])

        if (tagFieldNameForDisplay && lazySelectProps?.list) {
          const filter = `${fieldValue.map((tag) => `${tagFieldName} eq '${tag}'`).join(' or ')}`
          let data = await getItemsService(lazySelectProps.list, {
            filter,
            top: fieldValue.length,
          })
          data = data.value

          const newDisplayTags = fieldValue.map((tag) => ({
            key: tag,
            display:
              data.find((item) => item[tagFieldName] === tag)?.[tagFieldNameForDisplay] || tag,
          }))

          setDisplayTags(newDisplayTags)
        }
      }
    }

    initTags()
  }, [fieldValue])

  let rootRenderValue
  if (renderValue) {
    rootRenderValue = renderValue
  } else {
    rootRenderValue = (fieldValue) => {
      if (fieldType === MEDICAL_RECORD_FIELD_TYPE.DATE) {
        return dayjs(fieldValue).format('DD/MM/YYYY')
      }
      if (fieldType === MEDICAL_RECORD_FIELD_TYPE.DATETIME) {
        return dayjs(fieldValue).format('DD/MM/YYYY HH:mm')
      }
      if (fieldType === MEDICAL_RECORD_FIELD_TYPE.TIME) {
        return dayjs(fieldValue).format('HH:mm')
      }
      if (fieldType === MEDICAL_RECORD_FIELD_TYPE.TAG_SELECT) {
        // For tag select, display the tags as a comma-separated list
        if (Array.isArray(fieldValue)) {
          return <div>{fieldValue.join(', ')}</div>
        } else if (typeof fieldValue === 'string') {
          return <div>{fieldValue}</div>
        }
        return <div></div>
      }
      if (fieldType === MEDICAL_RECORD_FIELD_TYPE.SELECT) {
        // For select fields, find the matching option and display its label
        const selectedOption = settingSelectOptions.find(
          (option) =>
            (typeof option === 'object' && option.value === fieldValue) || option === fieldValue,
        )

        if (selectedOption) {
          return (
            <div>{typeof selectedOption === 'object' ? selectedOption.label : selectedOption}</div>
          )
        }
        return <div>{fieldValue}</div>
      }

      return <pre>{fieldValue}</pre>
    }
  }

  const renderField = () => {
    if (customField) {
      return customField
    }

    switch (fieldType) {
      case MEDICAL_RECORD_FIELD_TYPE.TEXT:
        return <Input {...rootControlProps} placeholder="Vui lòng nhập" />
      case MEDICAL_RECORD_FIELD_TYPE.TEXTAREA:
        return (
          <Input.TextArea
            {...rootControlProps}
            autoSize={{ minRows: 2 }}
            placeholder="Vui lòng nhập"
          />
        )
      case MEDICAL_RECORD_FIELD_TYPE.NUMBER:
        return <Input {...rootControlProps} type="number" placeholder="Vui lòng nhập" />
      case MEDICAL_RECORD_FIELD_TYPE.SELECT:
        return (
          <Select
            {...rootControlProps}
            placeholder="Vui lòng chọn"
            style={{ width: '100%' }}
            showSearch
            filterOption={filterOption}>
            {settingSelectOptions.map((option) => {
              // Handle both simple string options and object options with value/label
              if (typeof option === 'object' && option.value !== undefined) {
                return (
                  <Select.Option key={option.value} value={option.value} label={option.label}>
                    {option.label}
                  </Select.Option>
                )
              } else {
                return (
                  <Select.Option key={option} value={option} label={option}>
                    {option}
                  </Select.Option>
                )
              }
            })}
          </Select>
        )
      case MEDICAL_RECORD_FIELD_TYPE.TAG_SELECT:
        return <Input hidden />
      case MEDICAL_RECORD_FIELD_TYPE.DATE:
        return <DatePicker {...rootControlProps} format="DD/MM/YYYY" />
      case MEDICAL_RECORD_FIELD_TYPE.DATETIME:
        return <DatePicker {...rootControlProps} showTime format="DD/MM/YYYY HH:mm" />
      case MEDICAL_RECORD_FIELD_TYPE.TIME:
        return <DatePicker {...rootControlProps} showTime={{ format: 'HH:mm' }} format="HH:mm" />
      default:
        return (
          <Input.TextArea
            {...rootControlProps}
            autoSize={{ minRows: 2 }}
            placeholder="Vui lòng nhập"
          />
        )
    }
  }

  return (
    <>
      <span
        className={`${labelBold && 'fw-bold'} ${underlineLabel && 'underline'} ${labelClassName}`}>
        {label}
      </span>
      <Button
        hidden={!isEdit || !templateOptions?.[0]}
        className="ms-2"
        size="small"
        icon={<i className="fa-regular fa-square-plus"></i>}
        onClick={() => {
          setIsOpenSelectTextModal(true)
          setSelectedTemplateOptionContext({
            fieldName,
            options: templateOptions,
          })
        }}>
        Chèn mẫu câu
      </Button>
      {isEdit ? additionalButtons : ''}
      <div style={{ width: '100%' }}>
        <Form.Item
          rules={required ? [{ required: true, message: 'Vui lòng nhập' }] : []}
          name={fieldName}
          {...antdProps}
          noStyle>
          {isEdit ? renderField() : <div>{rootRenderValue(fieldValue)}</div>}
        </Form.Item>

        {fieldType === MEDICAL_RECORD_FIELD_TYPE.TAG_SELECT && isEdit && (
          <div>
            <div className="mb-2">
              <LazySelect
                {...lazySelectProps}
                key={`${fieldName}-lazy-select-${tags.length}`} // force re-render
                resetOnSelect={true}
                setSelectedUser={(item) => handleAddTag(item)}
                excludedKeys={tags}
              />
            </div>

            {/* Display tags */}
            <div className="mb-2">
              <div>
                {tags.map((tag) => (
                  <Tag
                    key={tag}
                    closable
                    onClose={() => handleRemoveTag(tag)}
                    style={{ marginBottom: '8px' }}>
                    {tag} - {displayTags.find((dTag) => dTag.key === tag)?.display}
                  </Tag>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      <Modal
        title="Chọn mẫu"
        className="custom-modal"
        width={1000}
        open={isOpenSelectTextModal}
        onCancel={() => setIsOpenSelectTextModal(false)}
        footer={[]}>
        <SelectFromTemplateText
          options={selectedTemplateOptionContext.options}
          onSelect={(text) => {
            if (!text) {
              return
            }
            const oldText = form.getFieldValue([selectedTemplateOptionContext.fieldName]) || ''
            const breakLine = oldText ? '\n' : ''

            form.setFieldsValue({
              [selectedTemplateOptionContext.fieldName]: `${oldText}${breakLine}${text}`,
            })

            setIsOpenSelectTextModal(false)
            setSelectedTemplateOptionContext({})

            // focus to this field
            setTimeout(() => {
              form.focusField([selectedTemplateOptionContext.fieldName])
            }, 1000)
          }}
        />
      </Modal>
    </>
  )
}

MedicalRecordField.propTypes = propTypes

export default MedicalRecordField
