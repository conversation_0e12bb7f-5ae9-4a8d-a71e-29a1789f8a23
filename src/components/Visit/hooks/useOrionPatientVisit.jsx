import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import lists from '../../../common/lists'
import {
  addListItemService,
  getItemsService,
  updateListItemService,
} from '../../../common/services'
import { formatHN, handleError } from '../../../common/helpers'

export const ORION_VISIT_QUERY_KEYS = {
  ORION_PATIENT_VISIT_VIEW: 'patientVisitView',
}

export const useOrionPatientVisit = (
  dateRange = [null, null],
  filterHN = null,
  filterSSConfirmationFlag = 'ALL',
  filterProcessingStatus = null,
  filterVisitTypes = null,
) => {
  const [startDate, endDate] = dateRange
  const queryEnabled =
    (!!startDate && !!endDate) ||
    !!filterHN ||
    !!filterSSConfirmationFlag ||
    !!filterProcessingStatus ||
    !!filterVisitTypes
  const queryClient = useQueryClient()

  const orionPatientVisitQuery = useQuery({
    queryKey: [
      ORION_VISIT_QUERY_KEYS.ORION_PATIENT_VISIT_VIEW,
      startDate,
      endDate,
      filterHN,
      filterSSConfirmationFlag,
      filterProcessingStatus,
      filterVisitTypes,
    ],
    queryFn: async () => {
      let filterConditionParts = []

      if (startDate && endDate) {
        filterConditionParts.push(
          `actual_visit_datetime ge ${startDate} and actual_visit_datetime le ${endDate}`,
        )
      }

      if (filterHN) {
        // filter by visit_code
        if (filterHN.indexOf('IPD') !== -1 || filterHN.indexOf('OPD') !== -1) {
          filterConditionParts.push(`visit_code eq '${filterHN}'`)
        } else {
          const formatedHN = formatHN(filterHN)
          filterConditionParts.push(`visible_patient_id eq '${formatedHN}'`)
        }
      }

      if (filterSSConfirmationFlag !== 'ALL') {
        const filterSSConfirmationFlagValue =
          filterSSConfirmationFlag === 'NA' ? null : filterSSConfirmationFlag === 'Yes'

        filterConditionParts.push(`ss_confirmation_flag eq ${filterSSConfirmationFlagValue}`)
      }

      // For Orion tab, only show ALL and WAITING_FULLFILL_INFO statuses
      if (filterProcessingStatus && filterProcessingStatus !== 'ALL') {
        filterConditionParts.push(`processing_status eq '${filterProcessingStatus}'`)
      } else {
        // If no specific status is selected, only show ALL and WAITING_FULLFILL_INFO for Orion tab
        filterConditionParts.push(
          `(processing_status eq 'WAITING_FULLFILL_INFO' or processing_status eq null)`,
        )
      }

      if (filterVisitTypes && filterVisitTypes.length > 0) {
        const values = filterVisitTypes.map((item) => `'${item}'`).join(', ')
        filterConditionParts.push(`visit_type_rcd in (${values})`)
      }

      // filterConditionParts.push('parent_patient_visit_id eq null')

      const filterCondition = filterConditionParts.join(' and ')

      const data = await getItemsService(lists.api_orion_patient_visit_view, {
        filter: filterCondition,
        orderBy: 'actual_visit_datetime desc',
        top: 100,
        count: false,
      }).then((res) => res.value)

      return data
    },
    enabled: queryEnabled,
    refetchOnWindowFocus: true,
  })

  const updatePatientVisitMutation = useMutation({
    mutationFn: async ({ id, data }) => {
      await updateListItemService(lists.patient_visit, id, data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [ORION_VISIT_QUERY_KEYS.ORION_PATIENT_VISIT_VIEW, startDate, endDate],
      })
    },
    onError: (error) => {
      handleError(error)
    },
  })

  const createPatientVisitHistoryMutation = useMutation({
    mutationFn: async ({ data }) => {
      await addListItemService(lists.patient_visit, data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [ORION_VISIT_QUERY_KEYS.ORION_PATIENT_VISIT_VIEW, startDate, endDate],
      })
    },
    onError: (error) => {
      handleError(error)
    },
  })

  const refetchOrionPatientVisit = () => {
    queryClient.invalidateQueries({
      queryKey: [ORION_VISIT_QUERY_KEYS.ORION_PATIENT_VISIT_VIEW, startDate, endDate],
    })
  }

  const isLoading =
    orionPatientVisitQuery.isLoading ||
    updatePatientVisitMutation.isLoading ||
    createPatientVisitHistoryMutation.isLoading

  const isError =
    orionPatientVisitQuery.isError ||
    updatePatientVisitMutation.isError ||
    createPatientVisitHistoryMutation.isError

  return {
    // Individual query results
    orionPatientVisits: orionPatientVisitQuery.data,
    orionPatientVisitQuery,
    updatePatientVisitMutation,
    createPatientVisitHistory: createPatientVisitHistoryMutation,
    refetchOrionPatientVisit,
    // Status
    isLoading,
    isError,
  }
}
